So, one of the things I want to do first is show you how to read a stack trace. Some of you may have already been reading these, but there's a lot more information in here than you probably know. Then I'll briefly show you some documentation around how you could get core dumps if you need them—and I pray you never do.

Let's start with a basic stack trace. I'm going to use the Go Playground for this—it's really amazing stuff. There we are on line seven with the function `main`, and `main` calls a function named `example`, passing three values: a slice, a string, and an integer. You see all of that. I'm going to be on the whiteboard in a second to show you how this stuff gets laid out so we can get a clearer picture. But for now, I just want you to look at how we're passing a slice value, a string value, and an integer.

When we call `example`, I'm forcing it to panic, which produces a stack trace—and this is the stack trace it produces. You probably already know how to read it because it's already telling you: we were on line eight, then on line twelve. When we were on line twelve, we called `panic`, which is on line 464. So we were on line twelve, there it is, and then we were on line eight. Most people can quickly figure out that this call stack—this trace, this stack trace—is showing us where we were in the call stack when everything blew up.

But what you don't realize is that all of the data you passed into these functions is also in this stack trace. It all looks like odd hexadecimal numbers, but it's all there—and I want to show it to you. This is why I always say: write precise APIs. Write precise APIs. Don't hide information going in, don't hide information going out. Because if you don't, you end up with code that's more readable, more testable, more maintainable. It gives the compiler a better chance to find bugs before runtime, and it makes these stack traces even more powerful.

Let's go back to the example. We called `example` with a slice—but what is a slice? The slice created with the `make` call tells us the following: a slice is a three-word data structure. Remember? It has a pointer, a length, and a capacity. In this case, the length is two and the capacity is four. There it is: pointer, two, four. That's the slice we're making on the fly.

The next parameter is a string. Where does a string live in Go? We learned that a string is a two-word data structure: a pointer and a length. The length here is five because there are five bytes. Finally, we have one more word of data—an integer with the value 10.

One, two, three, four, five, six words of data I've drawn on the board because we're passing six words of data into `example`. Let's go back to the stack trace. What I want you to do is tell me how many hexadecimal numbers I've highlighted. Guess what? One, two, three, four, five, six—there it is. If there were values being returned from this function, you would see more values here. The very first ones are the input words; the others are the output words.

Now, let's read these six words—I think you're going to be a little shocked. The first three represent the slice. Look at that: address, two, and four—just like on the board. Address, two, four. I've highlighted address, two, four. The next two represent the string—I've highlighted them: address and the number five. Address and the number five. And finally, `a` in hexadecimal is 10. There it is.

We know every value that was passed into `example` before the panic. So the more precise you are, the more information you have about what's going on. We don't want to write APIs that are generic. We want APIs that are precise, that expose semantic meaning. Be very precise with your data, be clean in your stack trace—it tells you everything. Between the logs, the code, and the stack trace, we can debug a lot of great code in Go.

Now, I want to show you another one of these, which is a little different. Notice in this piece of code, we call `example` again. `example` panics, but this time we're passing four bytes of data—half a word of data on our 64-bit platforms—into `example`. These stack traces show full words at a time, and this time we're only passing half a word. True, false, true, 25. Bools are one byte each—that's three bytes. `uint8` is one byte—so that's four bytes out of an eight-byte word.

When that happens, we get this stack trace: we get a single value. This is all we get—a single value. But if you know your system, you know this is little-endian, so we can read this data. Since we're showing words at a time, all of these bytes get compacted into a single word in the output. And because we're using little-endian, we read this from right to left. Every two digits in this value represent a single byte.

Watch what happens. This was the value that showed up. If we read it from right to left: `01`—that's true. `00`—that's false. `01`—that's true. Hexadecimal `19`—hey, that's 25. The `08` is garbage because we only filled up half a word.

Look at this—how amazing is this? We were able to take every two digits, which represents one byte, read it from right to left, and I know every value I passed into this function when it panicked. This is incredible—really powerful stuff that Go gives us in stack traces. And I don't want you to walk away from this. I don't want you walking away from it—it's too important.

Now, one other thing Go gives you is the ability to generate core dumps. Core dumps are really powerful. You can get these core dumps literally just by sending a `SIGQUIT` signal to your running Go application. If you think your Go application is deadlocked or you need to inspect what's going on, send a `SIGQUIT` signal to it. On your Mac keyboard, that's Control-Backslash.

When that happens, you'll get a dump of every goroutine. You'll be able to see what the registers are for the thread running that goroutine. And what's even more amazing is if you use the environment variable `GOTRACEBACK` and set it to `crash`, then you'll get a full dump—not just of your goroutines, but of the entire Go runtime and everything that's happening.

There's something else very cool about these core dumps. On Linux, there's a tool called `gcore`, and we can use `gcore` to get a dump of a running Go program. When you do that, it produces that kind of profile—it writes the dump of the running Go program, like what I'm showing you here: `gcore -o core.txt` for whatever PID is running.

And the Delve debugger—if you're looking for a debugger, Delve is it. The Delve team works very closely with the Go language team to ensure support. The Delve debugger knows how to read `gcore` dumps. You can take a running Go program, create a `gcore` dump, go into Delve, tell Delve, "Hey, this is the binary that we did it from. Here's the dump—you need the binary when you do it." And boom, you're now able to look at what was happening at that moment in time when you took the dump.

Amazing things. Thank God I've never needed any of these dumps—between the stack traces, my code, and logs—because I maintain really strong mental models. I've always been really okay with all of this. But it's all there for you if you need it: the stack traces and the core dumps. It's very, very powerful stuff in Go.