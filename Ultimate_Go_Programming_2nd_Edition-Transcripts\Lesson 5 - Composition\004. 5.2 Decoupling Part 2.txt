All right, let's go through a real, concrete example of what I mean by decoupling—solving a problem concretely first, and then asking the question: "What can be decoupled?" I want you to see the mechanics and the flow of this process. It's something I believe will really help you as you grow in your coding practice.

The idea of a concrete implementation can mean one of two things: it could be a prototype, or it could even be production-ready code. I strongly believe in prototype-oriented design because I don’t want you guessing—I want you knowing. A prototype might be something we put into production immediately. The key point is that we must get code into production faster—code that has integrity, code that allows us to start solving real problems. To me, technical debt isn’t just poorly written code; it’s code that never leaves your laptop, never makes it to production. We have to get better at shipping working software.

So let’s start with a problem. Here’s the scenario: a client comes to me and says, "<PERSON>, I don’t know if you know this, but we have a backing system called Xenia, and Xenia has a database." I say, "Yeah, yeah, I know all about Xenia." Then they say, "Well, <PERSON>, do you know about our system called Pillar? Pillar also has a database, and what’s special about Pillar is that it has an API that our clients use to build front-end applications that consume it." I reply, "Yeah, yeah, I know your business. But how do I know it’s all good?" They respond, "Well, <PERSON>, one of our clients who consumes data from <PERSON><PERSON> recently found out about Xenia and asked, 'Can you get that data out of Xenia and move it into Pillar? If you can, I’ll sign a very large contract. I want to consume that data.'" So the question becomes: <PERSON>, do you think we can move data from Xenia into Pillar?

Immediately, I recognize this as a data transformation problem. The first thing I need to understand is how complex that transformation is. My first question is: what latency can we tolerate when moving the data? They say, "No, <PERSON>, we can write a program that runs as a cron job every five minutes. We can move the data every five minutes." I say, "Five minutes is brilliant! Five minutes is eternity. I like that. Let me go look at it."

As I examine the problem, there are a few things I want to be clear about. First, I’m focused solely on the concrete problem at hand: extracting data from Xenia and moving it into Pillar. I’m not thinking about contracts, interfaces, or decoupling—not yet. That kind of thinking is for refactoring later. Right now, my only goal is to solve this specific problem. And the beauty of solving it concretely is that once I get it working and into production, I can then ask: "Now that this is working, what parts of this code can be decoupled to make it more adaptable as the data or business requirements evolve?" But that’s not my concern right now. My concern is moving data from Xenia to Pillar.

When I look at the problem, I identify four core challenges I need to solve, in this order. First, I need to learn how to connect to the Xenia database. If I can’t connect, nothing else matters. That’s problem one. Second, I need to determine what data hasn’t been moved since the last five-minute run. If I can’t solve that, nothing else matters either. Third, I need to establish connectivity to Pillar. And fourth, I need to insert the data into Pillar. These are the four fundamental problems I must solve. There are many small tasks involved, but at a high level, the challenge is: connect, identify data, connect again, and store data. That’s it. These are the problems I need to solve.

So I go back to my desk and start writing code. I come up with a solution. I don’t want you to focus on the full implementation right now—I want you to focus on the mechanics, the architecture, and the structure of what we’re doing. We’ll refactor later. Let’s look at the base prototype code that we could actually put into production to solve the immediate problem.

I’ve kept the data simple for clarity—obviously, in reality, it would be more complex, but that’s not the point. Focus on the flow and design. This is the data we’re moving, transforming, and inserting into Pillar.

Now, I turn my attention to the primitive layer. I’ve said before that I like to work from the bottom up: first the primitive layer, then the lower level, then the higher level. So here, I’m focused. The primitive layer API must do one thing, and do it very well. I decide that Xenia is a stateful system—I need to connect to it and maintain some state about what I’m doing—so I’ll build a type-based API. In other words, a data-oriented API, where data and behavior are tied together. This is common when dealing with stateful systems or when decoupling is a concern.

So I define a type called Xenia, which has fields like Host and Timeout. Let’s assume, for now, that I’ve already solved the connectivity issue—we’ll skip over that in this example. The next problem is: what is the next piece of data I need to move? Since this is the primitive layer, I’m focusing on just one piece of data at a time. So I design an API with a method called Pull, attached to the Xenia type. It takes a pointer to a data structure so we can share data down the call stack, which helps with memory allocations. I don’t want to pass data up—I want to pass it down, especially since this will be in a tight loop. The idea is that Pull knows how to find and retrieve the next piece of data that hasn’t been moved yet. Each call returns the next available piece.

Now, you might be thinking: "Bill, wait a minute—if there are ten thousand pieces of data, are you really going to call Pull ten thousand times? That’s going to be too slow!" And I’ll say: stop. If you’re telling me this API will be too slow, you’re guessing. Until I have a working program, with benchmarks and profiler data, we don’t know. I don’t want you optimizing for performance yet—I want you optimizing for correctness. What’s the simplest, cleanest way to solve the problem? If it’s fast enough, we don’t need to make it faster. If it’s not, we can make it a little cleverer—but even then, we should minimize cleverness. So please, stop worrying about performance right now. We need working code first. Until it works, we don’t know anything.

So I proceed. My primitive layer knows how to pull one piece of data—the next piece—from Xenia. A lower-level layer can handle batching; a higher-level layer can simplify everything through a single API. I haven’t implemented the actual logic—I’m using a random number to simulate whether we’ve pulled data, hit the end, or encountered an error. The implementation isn’t important. What matters is the API design and the fact that we’re simulating behavior.

Guess what? I’ve just solved the first two problems: connecting to Xenia and identifying the next piece of data to move.

Now I need to solve the next two: connecting to Pillar and storing the data. Again, this is a great use case for a type-based API because I need to maintain state and perform storage. Just like with Pull, I design a Store method that uses pointer semantics to pass data down, minimizing allocations. Store knows how to push the data into Pillar’s database.

Now I’ve solved all four problems. I know how to do the core operations. I’ve completed the primitive layer API, and I’ve written unit tests—let’s assume that’s done. This layer is complete.

Now I move to the lower level. This layer needs to pull multiple pieces of data and store multiple pieces. It needs to pull from Xenia and store into Pillar. And as I think about pulling and storing from these two systems, an idea comes to mind: how useful would it be to define a concrete type called System that represents both Xenia and Pillar? If I embed or compose the two concrete types—Xenia and Pillar—into a System type, then that System knows how to Pull and how to Store. I’m not embedding for state—I’m embedding for behavior. This is critical. Now I have a concrete type that encapsulates both Pull and Store behaviors, thanks to embedding. We’ll see how this helps, but for now, I go back to the lower-level API.

I decide I need a function called Pull. Remember: I prefer functions over methods when possible. I want to ask you this: why do I believe a function is always more precise than a method? This is true. Functions can be more precise because they require all inputs to be explicitly passed in to perform the data transformation. Methods, especially when poorly designed, can hide information. When designing method-based APIs, we must be extra careful not to hide state or dependencies, because hidden information leads to misuse and bugs.

Let me give you an example. Imagine I need to send an email to a user. I might have a User struct with fields like Name and Email, and probably others—Age, maybe. Now, someone might say: "Okay, I know how to do this." They’d use pointer semantics on the User and write a method called SendEmail. This is how we’re often taught: attach behavior to data. But this is a horrific API. It’s the opposite of what I want. This API is not precise—it’s full of potential fraud. Why? Because it doesn’t tell the caller what fields must be initialized for the function to succeed. I don’t want runtime failures because a value’s state is incomplete. That’s a major violation. The compiler should help us catch these issues.

Right now, SendEmail might only use Name and Email. But what if tomorrow I change it to require Age? Suddenly, every caller who doesn’t set Age will fail—and neither the callers nor the compiler will know about it until runtime. We won’t discover this until production. And production bugs are the worst kind—they cause stress and downtime. This API is generic, imprecise, and dangerous.

And by the way, this isn’t any better: a function that takes a *User pointer. That’s essentially just a method in disguise. We don’t want that either.

So how do we write a precise API? I want a function that says: "Give me a Name and give me an Email." That’s it. Nothing more, nothing less. This is maximum precision. It eliminates misuse because everyone knows exactly what’s required. It’s also easier and more precise to test—we’re not guessing about what fields need to be set. And if tomorrow I need Age, I will absolutely accept the cost of breaking the API. Most of us work in private codebases—breaking APIs is okay. I demand that the API break, so the compiler can tell us everywhere in the codebase where we’re going to have a problem before the code ever reaches production.

I have production-level code with functions that take 8, 10, even 12 parameters. The more precise, the better. Don’t fall into the trap of abstracting inputs—abstracting hurts testing, hurts usability, and hurts debugging. So avoid it.

That’s why I’m choosing a function-based API here. It should be our first choice when it’s reasonable and practical—which it is.

So Pull takes a pointer to Xenia and a slice of data values to populate. Look at how the lower-level API sits on top of the primitive API: we range over the slice using pointer semantics—because we should use pointers with data—and pass the address of each element into the primitive Pull method. Once we’ve pulled a batch, we do the same for Store. Store works with Pillar—right now, that’s the only thing we know how to store into. We range over the pulled data and call Store on each item. We can write tests to verify this all works.

Now we’ve completed the lower-level API, written its unit tests, and both layers are working together. Because the lower level is built on a solid primitive layer, it’s easier to write and more reliable.

Now we need the higher-level API to tie everything together. We create a function called Copy. Notice that Copy needs two behaviors: it needs to Pull and it needs to Store. This is where the idea of a System comes in. Remember the System type we created by embedding Xenia and Pillar? A System knows how to Pull and Store. Since Copy needs both behaviors, we can define it to take a System and a batch size. We’ll make one allocation based on the batch size—that’s acceptable. Then we enter a loop, calling the lower-level Pull and Store functions, passing in Xenia and Pillar through the System.

And just like that, we’re done with the higher-level API. We can even write tests for it if we want.

Now we’ve built every layer of the API, each sitting cleanly on top of the one below. We have clear mental models. If there’s a bug, we can quickly identify which layer it’s in and fix it.

Now, in the application, we create a System, initialize Xenia and Pillar—pretend connectivity is working—we call the high-level Copy function, pass in the System and a batch size of three, and guess what? We have a working program, implemented concretely. We take that program and push it into production.

Now it’s in production. We’ve solved the problem. But here’s the real question: are we done?

No—we’re only halfway done. We’ve written unit tests for every layer, but we’re not finished until we ask: what can change?

Because the next step in good design is to ask: what parts of this system are likely to change? And to answer that, you often need to understand the business, not just the technology.

In this case, what’s going to change? The systems. Once we’ve solved this problem, the business will come back and say: "Hey, Bill, did you know about a system called Bob? Bob has a database, and it would be great if we could move data out of Bob. And by the way, there’s also a system called Alice—she’s a new front-end API we’re building—and it would be great if we could start moving data there too."

So what changes? The systems change.

Now we need to ask ourselves: do we want to decouple this code to handle that change? If the answer is yes, then we take this concrete implementation and refactor it to support multiple systems.

And that’s exactly what we’ll do next.