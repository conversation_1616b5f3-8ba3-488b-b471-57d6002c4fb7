Alright, let's look at another polymorphic example here, and things are going to get a little more complicated—but that's okay, we're going to build up to this. Now I've got another interface here called Notifier, with one active behavior: notify. Again, I want to emphasize the relationship of behavior. Interfaces should describe behavior, not persons, places, and things. And here it is: in Notifier, we have this one active behavior, notify. We've got this single method, and that's it.

Now here's my concrete data—it's named user, with fields name and email—and we're on line 22. Remember what I can say: the concrete type user now implements the Notifier interface using pointer semantics. We were using value semantics before, now we're using pointer semantics. All I had to do was implement the notify method, and because that completes the full method set for Notifier, I can say it again: the concrete type user now implements the Notifier interface using pointer semantics.

Now I go ahead and look down here, and we have our polymorphic function again. What is this function saying? It's saying: I will accept any piece of concrete data—any value, any pointer—that implements the Notifier interface. It can't be asking for Notifier values because they don't exist; that's an interface type. Interface types are valueless. Pass me any concrete piece of data—any value, any pointer—that implements, that has the full method set of behavior for Notifier. And then we will call into that behavior from a decoupled state through the interface value. Brilliant.

So let's go through this program now. On line 31, I construct my user value—there it is, user right there—and we put Bill in it. There's my concrete data. Now just like we did before, on line 36, I call my polymorphic function using value semantics, which means we want to pass a copy of Bill across this program boundary. But something very odd happens. When I try to run this program, the compiler very quickly says, "Whoa, whoa, whoa, whoa, whoa! I'm sorry, but this piece of data you're trying to pass across this program boundary doesn't have the behavior it needs—it doesn't have the notify behavior."

And we're like, "What? What are you talking about, compiler? I did everything you asked. I defined a method named notify, and notify matches the notify method inside the interface. I have compliance! I've got a method right there named notify for the concrete type user that implements the Notifier interface using pointer semantics. Compiler, it's all there—why are you not letting me pass this value through? Why does this piece of data not implement the Notifier interface?"

Well, the compiler is absolutely right. And what the compiler is doing here, I call it love. The compiler loves you, and it doesn't want you to have an integrity issue in your software. If the compiler allowed you to use value semantics here, we would be in a tremendous amount of trouble.

Now I want to explain to you and show you where this integrity issue is. There are really two parts to it. But before I can do that, we've got to talk about method sets. There's a set of rules in the specification around method sets, and these method sets are there to protect us and our software.

I'm going to write the rules for method sets as they relate to the specification, and then I'll rephrase them slightly to make them easier to understand—so you can see where the integrity issues are with line 36. But remember: this is all love. Don't look at it any other way. This is the compiler showing you love.

Here we go. This is what the specification says: If you're working with a value of type T—so, value semantics—then only those methods that use value receivers belong to the method set for this value. That's it. But if you're working with a pointer—pointer semantics—then both your pointer receiver methods and your value receiver methods belong to the method set. In other words, all the methods you declare for that concrete type exist for pointers, but only the value receiver methods exist for values. The pointer receiver methods are excluded from the method set of values.

If we quickly go back and look at what's happening here, notice that I implemented the Notifier interface using pointer semantics. Now I'm trying to use value semantics. I'm mixing semantics, and the compiler is saying, "Whoa—you're trying to create a copy of T, but guess what? T doesn't have any methods." The compiler was right, because the pointer receiver method wasn't included in the method set.

The real question now is: why? Why can't we include pointer receiver methods for a value of T, but we can for pointers? And why do value receiver methods apply to both?

There are two integrity issues here—one I'd call minor, one major. Let's look at the minor one first.

What if I told you that not every value you work with has an address? Think about this. What if you couldn't take the address of T? Well, if you can't take the address of T, you can't call a pointer receiver method. You cannot use pointer semantics if something can't be shared. Remember, integrity is about 100%. If you can't have something 100% of the time, you can't have it at all—you're setting yourself up for an integrity issue.

Look at this code. I define a type named duration based on an integer—duration is based on int. Then I implement the Notifier interface using pointer semantics: I have a method named notify with a pointer receiver. But look at what I do on line 18: I take the literal value 42—which we know is a constant of kind int—convert it to a value of type duration, and try to call notify. The compiler says, "Dude, I'm sorry—this value has no address." And the compiler is right. At the end of the day, this is a constant. It's a constant of type duration, and constants only exist at compile time. They never end up on the stack or the heap. There is no address. Even though it satisfies the interface, there's no address—so the pointer receiver method can't be applied.

We're at least getting compiler messages here. This is a minor issue for me—not the big one, not the one that's full of hugs and love. But I want you to understand: if you can't have something 100% of the time, you can't have it at all. So we can't assume we can always get the address of T. But if you already have a pointer to T, then we know it's in memory—so we can always use the address, or we can always make a copy of the value the pointer points to. You can always do that. But you can't assume the address exists for a value.

But what's the bigger issue? Why are these rules really in place? Remember, this stuff has been known for a decade.

Let's look at this differently. When we talk about decoupling, I really want to focus on the behavior side, not the data side. And when we do that, something very interesting happens.

If we read this chart from right to left—focusing on behavior—something really interesting emerges. The chart says this: If you've chosen pointer semantics—and remember, we define the type, we choose the semantics, and then we implement—then you can only share. If you've chosen pointer semantics, the only thing you're allowed to do is share, because that's the only safe thing to do. If you choose value semantics, then you should make copies.

But I told you one thing: sometimes it's absolutely safe to share a value even if you're working with value semantics. I don't want to change semantics unnecessarily, and there are times when sharing is safe—like in decoupling or unmarshaling.

But what this chart also says is: it is never safe to make a copy of the value that a pointer points to. It's never safe. So if you're in pointer semantic mode, you're only allowed to share—you're never allowed to make a copy of the value the pointer points to.

How amazing is this chart? When we look at it from the behavior point of view, it all begins to make sense.

The compiler is saying: if you chose pointer semantics, you're only allowed to share. If you switch back to value semantics, that's a major, major violation of the rules. If you're using value semantics, please make copies—give the interface its own copy. But there are times when you might need to share. However, we never, ever, never want to go from pointer semantics to value semantics. It's a major violation.

What the compiler has done here is say: when we're storing data inside an interface, we're never going to allow ourselves to break this fundamental law—going from pointer semantics to value semantics. No, no, no—we're never allowed to make a copy of the value that the pointer points to. You cannot assume that is safe.

So when we come back and look at this code, what we realize is: we implemented the interface using pointer semantics, which now means we can only share data with the interface. We're no longer allowed to make copies of the data—which is exactly what I was trying to do on line 36.

So if I turn around and say, "Okay, let's maintain the right rule here—since we're using pointer semantics to implement the interface, I can only share the data with the interface," then I pass a pointer to user, and boom—it works.

How cool is this?

This chart is here to protect us, to maintain the right level of integrity when we're dealing with decoupling. If you're using pointer semantics—which was chosen when you defined the type and declared the methods—then you can only share data. If you're using value semantics, make copies—but you also have the ability to share when necessary.

But this—going from pointer semantics to value semantics—is never allowed. Never. Using pointer semantics, you may not make copies of the value that the pointer points to.