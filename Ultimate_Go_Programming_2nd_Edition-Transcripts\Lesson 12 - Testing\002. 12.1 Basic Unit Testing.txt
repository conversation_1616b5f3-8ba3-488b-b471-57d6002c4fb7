Alright, we're about to start learning how to write unit tests in Go. I love that unit tests are integrated into the Go programming language and into the tooling. It's a minimal solution, which is all you need—minimize, reduce, and simplify. Now, there are third-party packages out there; GoConvey is probably my favorite. But I really want to avoid using third-party stuff because it adds extra dependencies and complexity to your code. Normally, I'm not a fan of bringing in third-party packages just to provide stronger testing support, because you don't really need them. I'm going to show you how it all works right now.

<PERSON> said this, and I really love it: "A unit test is a test of behavior whose success or failure is wholly determined by the correctness of the test and the correctness of the unit under test." One thing we really need to validate is that our tests are adding value, and also that we're not over-unit-testing or under-unit-testing. This stuff is complicated, but today we're going to focus on the mechanics. I'm going to show you how I write unit tests—not necessarily how you must write them, but I want teams to be consistent. There's nothing more frustrating than when everyone is doing things differently, and because Go's solution is minimal, that inconsistency is quite possible.

We're going to go through a basic unit test here. I'm using VSCode to demonstrate everything. We've been using `go build` throughout the class, but today we're going to start using `go test` quite a bit. We'll also be using `go tool pprof`, `go tool trace`, and other parts of the Go tooling beyond just the compiler. If you want to write tests, your files must have an underscore test suffix. Remember, Go is about convention over configuration, and we use the `_test` convention to denote files that are isolated to the testing tool. These files won't be built or compiled into your production binaries. However, `go test` will run the compiler—we'll see some of that today.

Once you create a `_test` file, it should ideally be in the same package as your source code—whether that's an API, a binary, or whatever you're building. You can either place the test in the same package name, or more commonly, use the same `_test` convention for the package name. Why choose one over the other? If you want to write tests that access unexported APIs, you'll use the same package name as the source code. If you want to write unit tests that reflect how users will interact with your exported API—which is what I believe in—then you'll use the `_test` package convention. Go allows these test files to exist in their own package even though they're in the same folder, meaning you have to import your package like any other user and work with only the exported API.

What often happens for me is I'm writing a new package and starting with unexported APIs, so I might begin by writing tests in the same package to test those unexported functions. But eventually, when I build the exported API that uses those unexported ones, I start removing the unexported tests and keep only the exported ones. I don't get hung up on writing tests I might throw away later—I want to ensure my code works throughout development. I don't consider that a waste of time; I consider it a way to be more productive. During development, I probably write many more unexported tests that eventually get discarded because the exported tests end up providing the same code coverage I need. You have to find your own workflow. That's mine.

I always want to make sure unexported APIs are very testable and exported APIs are very usable. It's a delicate balance. These are my philosophies.

Let's start with a very basic unit test in Go. I have a `_test` file with `package example`, which matches the folder name. I'll show you my approach to unit testing—you don't have to follow it exactly. I'm a bit particular about verbosity because when tests run in CI, I want a lot of information. I believe in not hiding information. I've defined two constants using Unicode characters for a checkmark and an X—you'll see how I use them. The key thing is having a function named `Test`. This function must start with `Test` and be exported. It's critical that the function is exported, or the testing tool won't find it. It's also critical that whatever comes after `Test` starts with a capital letter. If it doesn't, the testing tool won't recognize it, and you'll go crazy.

Some people like using an underscore after `Test`, like `Test_Something`. I'm not a fan because we don't typically write functions that way in Go. There are some conventions around examples where underscores matter, but overall, I avoid them. It's not wrong if you do it—`go test` will still find the function—but you won't see me doing it.

Another critical point: the function must not only start with `Test` (capital T), but the next character must be a capital letter or an underscore, and it must accept a `*testing.T` pointer. That `*testing.T` is your integration point with the testing tool. The `T` pointer provides the API we need. It includes functions like `Error`, `Fail`, `Fatal`, `Log`, and others. We'll focus on this API.

The `Log` function adds verbosity to your test—it doesn't affect whether the test passes or fails. It's just extra tracing information. I use `Error` when I want to log information and mark the test as failed, but continue executing any remaining code in the test function. I use `Fatal` when the test has failed and we're done—there's no reason to run anything else because we're in such a bad state that further execution is irrelevant. You'll see me use `Error`, `Log`, and `Fatal`, and we'll use other parts of the API as needed.

The whole idea of a unit test is to validate that something is working. Usually, we're validating that an API—exported or unexported—behaves as expected: given certain input, it produces the expected output. We're unit testing the expected behavior, the semantics of what we expect the API to do. That's what we're always trying to test.

Here’s what I’m doing: I want to unit test an HTTP GET call. I want to validate that the semantics of the GET call are correct. So I set up some basic input—I’m using the URL to the RSS feed for my blog—and I expect to always get back an HTTP 200 status code. I like the structure of "given, when, should" when writing unit tests. "Given" is why we're writing the test, "when" is the data or scenario we're testing, and "should" is the expected outcome. I like to break this into three distinct code blocks.

You’ll see me use artificial code blocks with curly braces. These define a new scope, but for me, they serve as readability markers—this is the "given" section. You can have multiple "given" sections in a test function if that helps your organization. So, given the need to test downloading content, when we use this URL and expect this status code—our inputs and sometimes expected outputs—I’ve labeled the test as "test zero" and used some tabbing; you’ll see the output shortly.

Then we start the actual test code. This code should be as close as possible to production code. More importantly, if a function returns an error, you must check it. I don’t want to see blank identifiers (`_`) in test code—you’ll get into trouble that way. Error handling must be part of the "should" logic.

I value consistency in output. Whether the test passes or fails, I want to see nearly identical messages. Yes, there’s a little duplication—I told you, I’m a bit obsessive. Not many people do this, but I like it. Consistent output improves readability when things go wrong, which is why I do it.

We should be able to make the call without an error. Whether it succeeds or fails, that outcome is clearly stated at the beginning of the output—I’ll show you. If there’s an error, we also display what it was. I use `Fatal` here because if the call fails, there’s no point in continuing. But if it succeeds, we defer closing the response body, just like in normal code, and then perform our checks.

In this case, I’m checking that the status code returned is the expected 200. Now you’re seeing my pattern for handling errors. This is my `Fatal` pattern: always use an `if` statement for the negative case. If the condition fails, we call `Fatal` and stop. Otherwise, we proceed. For non-fatal errors, we might use `Error` and continue, which requires an `if-else` structure.

Now I’m testing the positive case: if it succeeds, we should get success; otherwise, on the failure path, we should see failure.

I have a test. I’ve already navigated into this local directory—you can see `example1_test.go`. Let’s run the tests in a few different ways to see how this basic test works.

I can simply run `go test`. Nothing more. Since everything passes, it just says "pass" and shows that the test took almost 900 milliseconds.

But what if I want to see the output even when the test succeeds? I can use `-v`. The `-v` flag enables verbosity. Now you can see why I structured the test output this way. It says "given the need, test zero, when we use this data"—you see the checkmarks. To me, this is very clear. If you brought in a third-party package for this, the output might look similar, but I’m showing you how to achieve it with minimal effort—just consistent team practices.

Now, let’s make this test fail. I’ll artificially change the expected status code to 400. Let me clear the output first. Now, if I run `go test`, notice that when the test fails, it automatically switches to verbose mode—no need for `-v`. That’s really nice. When tests pass, the output is clean and quiet. When they fail, we get all the detailed output we prepared. This is exactly what I want.

You can see visually: there’s an X, indicating failure. It says we expected 400 but got 200. I’m trying to make the test output give a very clear indication of what went wrong. It even tells us the exact line—line 35—where the failure occurred. This is excellent.

This is our basic testing setup.

Go also supports the `-run` flag. What’s nice about `-run` is that it uses a regular expression to filter which test functions to run. For example, I can say `-run ^down` and it finds `TestDownload`, which is pretty cool. How you name your test functions affects how easily you can filter them across a large test suite using the `-run` flag.

This is our basic unit test. I’ve shown you some command-line options. How much verbosity and consistency you want is up to you, but I love this basic API.