Now we're going to talk about constants, and constants are really a fascinating part of the language for me because of the way they're implemented. Again, remember that Go is all about costs and trade-offs, so let's discuss the mechanics around constants—I find them really interesting—and I'll show you some of the cooler aspects of how they work in the language. You'll end up using constants in almost every program you write.

One of the really interesting things about constants, for me, is that they only exist at compile time. Constants have a much different feel and flavor compared to regular variables. Look at lines 15 and 16: I'm declaring two constants, but these are constants of a kind—a pattern I've mostly only seen in Go. Most of the time, we think of constants as read-only variables, but that's absolutely not the case in Go. When we look at constants, they can come in this form, which is "of a kind." Notice there's no type information during the declaration of these constants on lines 15 or 16. Instead, their kind—whether integer or float—is determined by the value on the right-hand side of the assignment.

Right below that, you see constants of a type: one of type int and one of type float64. The big difference between constants of a kind and constants of a type is that constants of a kind can be implicitly converted by the compiler. This is particularly interesting because, as we discussed with struct types, there's no implicit conversion of concrete data—but when it comes to constants of a kind, that rule doesn't apply. Constants of a type, however, are bound by type rules: once something has a type, implicit conversion is no longer allowed. But with constants of a kind, we gain flexibility, and it's a very powerful mechanism in Go, especially for code readability.

So here we have ui and uf—two constants of a kind, kind int and kind float—and ti and tf, which are constants of type int and type float. If we continue exploring the idea of kinds, there's something special in Go called Kind Promotion. Kind Promotion defines how values promote: floats promote over integers, and typed constants always promote over untyped (kind-based) ones.

On line 24, I had to comment out a line because once I made the constant a typed uint8, it became bound by the rules of that type and could only hold values within the range of an 8-bit unsigned integer. But what's also interesting about constants of a kind is that, because they're not type-bound, they're technically not limited by precision. If you look at the Go specification, it states that a constant must have at least 256 bits of precision. This makes constants of a kind—and the compiler when handling them—function almost like a high-precision calculator.

Now, look at line 30. I want to show you something very interesting. We're multiplying the value 3 by 0.333. On the surface, we have an integer and a floating-point number. Normally, you can't have implicit conversion between different types. We could argue that these are two different types—an int and a float—but they're constants. Literal values in Go are constants of a kind; technically, they're unnamed constants of a kind. Through Kind Promotion, the kind integer 3 is promoted to kind float. Now both sides of the multiplication are of kind float, and the variable "var" ends up being of type float64. This is very powerful: we can work with literal values from a kind perspective without worrying about explicit conversions, and promotion ensures everything works correctly.

But remember, we're dealing with up to 256 bits of precision when working with constants of a kind. When we assign the result to a variable—like "answer"—we're reducing that down to 64-bit precision, which may involve some precision loss. However, floating-point numbers are already imprecise due to IEEE 754 binary representation, so this is expected.

Look at line 33: this time we're dividing a kind int 1 by a kind float 3.0. Again, the integer promotes to kind float, and we get what we'd consider the exact representation of 1/3. In the past, we used to refer to constants of a kind as "exact" because of their high precision—they behaved like exact numbers. So we can think of "third" as truly 1/3, even though when stored in a 64-bit float, some precision is lost.

But on line 36, there's no promotion. Both 1 and 3 are of kind int. We perform integer division, and the result is 0—exactly what we'd expect, with everything staying within the integer kind.

I also mentioned that we can promote from kind to type. Look at line 40: we're creating a constant of type int8 with the value 1. It's still a constant, still compile-time only, but now it's bound by the int8 type. This is where the power of kind and type comes together. On line 41, we multiply a literal value of kind int (2) by the constant of type int8. Through promotion, the kind 2 value promotes to int8 and becomes a typed constant. Now both operands are of the same type, and the result, "two," is a constant of type int8.

These are the mechanics of constants of a kind, constants of a type, and the promotion rules. I want to show you how powerful this is. But first, I want to emphasize that the idea that constants exist only at compile time and have high precision—minimum 256 bits—is real. It's hard to demonstrate this directly since constants don't exist at runtime, but look at how I've declared the constant "maxInt." It represents the maximum signed 64-bit integer. But on line 14, I'm storing a number much larger than any 64-bit variable could hold. These are integers, but with 256 bits of precision, the compiler accepts this assignment. Without that high precision, this wouldn't be possible.

When I run this, the compiler accepts line 14 even though the number is too large for any 64-bit variable. If I try to create a constant of type int64 with the same value, it won't compile—the number exceeds the 64-bit limit. And I've only scratched the surface of what I could store in such a constant; I could keep adding digits because I have 256 bits of precision. The compiler truly acts like a high-precision calculator for constants.

Let me show you a more practical use of constants and how kind and type promotion make our lives easier. Look at the constant "tier" of type Duration. This is a second way to declare a type in Go: the named type Duration is based on int64. This is not an alias; we have two distinct named types. We're using int64 as the underlying memory model for Duration. I only do this when the new type has its own representation and meaning—which it does here in the time package. Duration represents time in nanoseconds, not just an int64.

Look at these constants—they demonstrate a clean, practical way that kind and type work together. We define a typed constant "Nanosecond" representing one nanosecond of time, based on Duration. Then on line 16, we take the literal value 1000—a constant of kind int—and multiply it by the typed constant Nanosecond. The 1000 promotes to type Duration, and the result is another constant of type Duration. This is amazing: it looks simple, but the engineering behind it is powerful. All these constants end up as type Duration, representing the correct int64 values for time units.

Even more compelling is the Add method. We'll discuss methods later, but focus on the parameter: Add takes a value of type Duration. You might expect only Duration values to be allowed. But one of the interesting things about constants of a kind is that they can be implicitly converted when compatible. This confused me when I first learned Go.

Look at line 39: I call the Now function from the time package to get the current time. Then on line 42, I call Add and pass the literal constant -5—a kind int. You might think, "Wait, that's not a Duration!" But because it's a constant of a kind, the compiler allows implicit conversion. It's interpreted as -5 nanoseconds. This flexibility is powerful but can be surprising. It's also one reason why Go doesn't support traditional enumerations—we can't get strict type safety when constants are based on built-in types, which is the only kind allowed since constants exist only at compile time.

Look at the timeout constant: 5 is of kind int, and time.Second is of type Duration. The 5 promotes to Duration, and timeout becomes a constant of type Duration. This supports the API cleanly and is a great example of API design leveraging both typed and kind-based constants.

The one thing you cannot do is this: if you convert the constant -5 (kind int) into a variable of type int64, you cannot pass it to Add. That's because it's now a named type value—int64—and Add only accepts Duration. Constants of a kind can be implicitly converted in calls, but any value based on a named type must match exactly. Type compatibility is strict everywhere else.

This is very interesting and powerful stuff around constants.

Now, there's one last feature you'll likely use in production code: the keyword iota. It's interesting and can be confusing at first. Notice I'm using a block—something we can do with vars, types, and imports. Here, I'm grouping constants using parentheses so I don't have to write "const" repeatedly. Iota works very well in constant blocks.

When a constant block starts, iota begins at 0. Each time it's used within the block, it increments by 1. If I run this, the output is 0, 1, 2. We get automatic incrementing for free.

Most of the time, because we want this incrementing behavior, we do it like this: assign iota only once to the first constant, and the rest automatically increment. The output is the same—0, 1, 2—but without repeating iota.

You'll also often see this: we don't want to start at 0, so we set the first constant to iota + 1. Since iota starts at 0 and increments per line, the first constant becomes 1, then 2, then 3. This pattern is used in the log package.

Sometimes, you want constants that represent bit flags. Notice here we use iota to shift bits left: 1 << iota gives us 1, 2, 4, 8, 16, 32. You'll see this pattern often with constants.

Iota is a powerful mechanism when creating sets of constants with unique IDs—it lets the language handle the numbering automatically.

So constants are very powerful in Go. Remember: there are two kinds—constants of a kind and constants of a type. Literal values in Go are constants of a kind; they're unnamed. Constants of a kind can be implicitly converted by the compiler, which is why Go doesn't have traditional enumerations with strict compiler protection. Once a value has a type, the full rules of type safety apply, restricting conversions. Constants of a kind can have up to 256 bits of precision—Go gives us a high-precision calculator for compile-time values. And again, all literal values are constants of a kind.