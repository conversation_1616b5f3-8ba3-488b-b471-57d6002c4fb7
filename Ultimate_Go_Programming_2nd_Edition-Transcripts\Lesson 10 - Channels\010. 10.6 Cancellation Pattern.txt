This is the last basic pattern I'm going to show you, and then we're going to start looking at some higher-level code that uses all of these patterns. It's cancellation. Cancellation deadlines are critical to software because a task or a request cannot take forever. We're going to use the context package from the standard library for this, so I'll use this code to demonstrate some basic mechanics of how the context package works. But ideally, I wouldn't want to see this kind of low-level code in production—I'd want to see the proper use of the context package, which I'll show you next.

Here's cancellation. Notice we're using a buffered channel of one, which gives us some delayed guarantees. It's not a full guarantee, but we also won't be completely blind to when things happen. We're still signaling with string data. Let's walk through it. We are the manager, we have our path of execution, and we've created a buffered channel of one. We use the familiar pattern: wait for a result. We start an employee goroutine, and that goroutine knows exactly what work it needs to do. The key point is that this might be work we someday want to cancel. If we're already doing the work and waiting for it to finish, we can't cancel it—we're blocked. That's where we need another goroutine if we want the ability to cancel. We can monitor progress and then tell that other goroutine, "Dude, stop."

Here's what happens. The worker goroutine does the work. If the work finishes, it attempts to send a result back to us. That send can happen immediately because we're using a buffered channel of one—the data goes into the buffer without requiring a corresponding receive on the other end. So we're safe. Let's imagine we're at line 227: we don't know how long the work will take. But on line 232, after creating the employee, we set up the `tc` variable, which gives the work a deadline of 100 milliseconds. If you look back at line 227, I've simulated that the work could take anywhere from one to 500 milliseconds. So there's a good chance it won't finish in time. We're not willing to wait longer than 100 milliseconds—if it takes that long, we have to move on.

How do we set up this scenario? We use a `select` statement, because `select` allows us to handle multiple channel operations—both sends and receives—simultaneously. Look at the first case: it's a channel receive from the buffered channel. We're blocked, waiting to receive data from that buffer. As soon as data arrives, we unblock. The second case is a channel receive from our timer. I have two channels here: one receiving from the worker, and one receiving from the timer. The call to `time.After` returns a channel, and the runtime will send the current time on that channel after 100 milliseconds. So the runtime will come in and perform the send for us at the 100-millisecond mark. Or, the worker goroutine might finish earlier and send its result, which fills the buffer and allows the receive to proceed.

Which one happens first? I have no idea—that's the point. If the worker sends its result within 100 milliseconds, we execute the first case. If not, the timer fires and we execute the second case. Either way, we have a guarantee that we'll move on within 100 milliseconds or less.

Now, there's a big caveat to this pattern that I want you to understand. There's a bug we'd introduce if we used an unbuffered channel instead of a buffered one. If we used an unbuffered channel, we'd have a serious problem. Let's walk through that scenario. Suppose we don't use a buffered channel of one—life gets bad very quickly.

We have our goroutine. With an unbuffered channel, we're relying on guarantees—specifically, that a sent signal is actually received. The employee goroutine starts, goes off, and does its work. Back on line 234, we're in a `select` again: we're either waiting to receive from the employee or from the timeout channel. We don't know which will happen first. But suppose the timeout happens first. We receive from the timer, and we move on. That means we're no longer waiting on the channel receive from the employee. We've exited the `select`.

What happens then? Eventually, the employee finishes its work on line 227 and tries to send the result. But with an unbuffered channel, a send requires a corresponding receive to complete. There's no one listening anymore—this send will block forever. That's a goroutine leak. This is a classic goroutine leak.

Goroutine leaks eventually cause memory issues—you start leaking memory. But Go is very good at cleaning things up, so sometimes these leaks don't manifest for hours, days, or even weeks. But the danger is real. When dealing with cancellation patterns, the manager goroutine (us) might walk away from the worker goroutine, and there's no direct way to tell that worker to stop. Sometimes you can through network calls, but often you can't. That's why we must ensure the worker doesn't leak—and using a buffered channel of one solves that. With the buffer, the send can complete even if no one is receiving, because the data goes into the buffer first. The send happens before the receive.

These are the things we need to consider. If you can visualize goroutines as people—manager and employee—always put yourself in the role of the manager. Think of these goroutines as doing work on your behalf. When you start visualizing this orchestration, the interactions between goroutines, you can begin to see whether you'll block, whether you'll face high latency, or whether you can reduce it. You can use `select` and case statements to handle drop patterns. And once you start thinking about signaling semantics and visualizing how goroutines interact, you can start doing a whole range of powerful things.