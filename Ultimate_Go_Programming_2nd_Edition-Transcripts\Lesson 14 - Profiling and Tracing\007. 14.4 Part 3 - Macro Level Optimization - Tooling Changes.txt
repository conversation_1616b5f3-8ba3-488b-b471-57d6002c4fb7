So we just went through the exercise of looking at a memory profile for that running Go program because we had the debug pprof endpoints bound into the server. I'm currently running on 1.11 Beta 1. By the time you see this, 1.11 will well have been released, and 1.12 is on the horizon. I'm really excited to have been running on 1.11 because you're starting to see some of the really big changes that are coming.

As I was doing the last section, I’ll be honest—I started seeing some things that were brand new to me, and I feel it's very important to lay this out now while I have the opportunity. This way, if you're still running on 1.10, you won't be confused later. Now that we're on 1.11, some things appear to be getting easier, and I suspect much of this is also in preparation for upcoming UI tooling.

There's a new endpoint: the debug pprof view for allocs. This is new—we didn't have it in 1.10. One thing that initially confused me a bit was seeing this endpoint called "allocs" alongside the existing "heap" endpoint. We've always had the heap endpoint, and we've traditionally used it to get both in-use space and allocation data—both in-use allocations and total allocations. Specifically, we've always used the heap URL to get in-use space and allocs space, which is why you saw me use the allocs_space flag on the command line earlier. That was necessary when using the heap endpoint.

Now, I'm on 1.11, and I want to show you something. Let me go back and use the traditional heap URL without any flags. I want to highlight this because I think it's going to be important, and I don’t want you to get confused later. So watch what happens: I run `go tool pprof` using the heap URL. Look at the default type—it's inuse_space. Remember, that shows only what's currently in the live heap. If I run `top 40 cumulative`, I don't see anything meaningful because the heap sample is effectively empty. If we go back to the browser, recall what it says: the heap is a sampling of memory allocations of live objects. That means when you use the heap URL, you're defaulting into inuse_space—this has always been the default, whether you're using the endpoint or analyzing memory profiles from files.

But now watch what happens when I use the allocs URL without any switches: `pprof allocs`. Notice that this URL automatically defaults the profiler into allocs_space, not inuse_space. So one of the things I’ve learned is that the new allocs URL defaults the tool into allocs_space—you no longer have to manually switch from inuse to allocs. If I run `top 40 cumulative` now, I see the same data we saw before. In the last video, when I ran this, I manually added the allocs_space flag to the allocs URL—that was redundant, because the allocs endpoint already defaults to allocs_space.

What's interesting is that you can still use the heap URL and explicitly specify allocs_space, and it will switch to allocs_space and give you the same `top 40 cumulative` result. So both URLs—heap and allocs—give you access to the same underlying memory profile. I want to be clear: this isn’t a different memory profile. The data is the same; the only difference is the default mode the tool enters. The heap URL defaults to inuse_space (live objects), while the allocs URL defaults to allocs_space. But because it's the same profile, you can override either one from the command line, as we just saw.

For example, I can now switch to alloc_objects—let me try that. I haven’t run this in a while. Now we’re in alloc_objects mode. Run `top 40 cumulative` on the sort, and now we see the percentage of objects being allocated. I personally prefer looking at space rather than object counts, but both views help clarify what you're actually analyzing.

To sum up: as of Go 1.11, you now have two distinct endpoints for examining memory allocations. The traditional one is heap, and the new one is allocs. The only difference between them is the default profiling mode. If you use the heap endpoint, the default is inuse_space, but you can override it on the command line. If you use the allocs endpoint, the default is allocs_space—which is convenient because you no longer have to remember to specify the flag. Of course, you can still override it if needed. This is all relatively new functionality introduced in Go 1.11.