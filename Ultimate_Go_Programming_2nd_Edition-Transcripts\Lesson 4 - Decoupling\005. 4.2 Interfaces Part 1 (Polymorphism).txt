So we just finished the topic on methods, which teaches us the mechanics behind how to add behavior to data. I mentioned that I want this to be the exception, not the rule. But now I want to start showing you the mechanics—and later we'll explore the semantics—of interfaces. Interfaces give us the ability to achieve polymorphism.

There's a quote from <PERSON>, the inventor of BASIC, that perfectly describes what polymorphism is: polymorphism means you write a certain program and it behaves differently depending on the data it operates on. I'd like to refine that slightly: polymorphism means a piece of code changes its behavior depending on the concrete data it is operating on.

This is critical. I keep emphasizing that concrete data drives everything—concrete data and semantics drive everything. And here we are talking about decoupling, which focuses on behavior. But what's driving that behavior? The data. So when should a piece of data have behavior? One strong, practical reason is when we need to implement polymorphism, because that polymorphism gives us levels of decoupling.

That’s when data should have behavior—when we need decoupling and want to process all different types of concrete data using a single piece of code. That’s valuable. Now, there may be other times when you want data to have behavior, such as when designing a stateful API—one that must maintain some internal state. But we must be very careful with API design in those cases. We’ll discuss those considerations as we move forward.

Now, I want to give you an example of polymorphism in Go and reinforce the idea that concrete data drives polymorphism because the concrete data has behavior. In this program, we start on line 10 with the `reader` interface. Notice we use the keyword `type`. This type `reader` is not based on a struct—it’s based on the `interface` keyword. It has one active behavior: `read`, which takes a slice of bytes and returns an int and an error.

Let’s pause for a moment and reflect on the `reader` interface, because this concept always boggles my mind. An interface is a type, which means I can declare a variable of that type—for example, `var r reader`. I can do that. But what’s mind-blowing is that an interface type is not real. We must make this very clear: interface types are not real. The variable `r` is not real—there’s nothing concrete about an interface type.

Struct types, yes—those are concrete. That’s real data we can manipulate and work with. But interfaces are not real. They only define a method set—a contract of behavior. There is nothing concrete about an interface, nothing at all. There is an implementation detail behind `r`, but from our programming model, `r` does not exist. It is not real. This is a crucial concept as we continue analyzing this code.

I’ve defined this interface type with one active behavior: `read`. One thing to emphasize, as we’re focusing on mechanics now (and will discuss semantics more later), is that your interfaces should define behavior—verbs. From my perspective, we’re reading, writing, running, printing. Interfaces are about behavior. I don’t want to see interfaces that describe nouns—like an `animal` interface, or `house`, or `user`. Those are not behaviors; they’re things. They represent concrete data. The more your interfaces focus on behavior—like `reader`, which has the active behavior `read`—the better off you’ll be with decoupling, because again, we’re focused on behavior. That’s it.

We haven’t been taught this way in traditional object-oriented programming, where we often try to decouple everything. My approach is to go back to using concrete data—because that’s where the real problem lies—and decouple only what we need to, which is behavior. I want to maintain this philosophy, these mechanics and semantics, throughout our entire program.

Now, I have the `reader` interface with one active behavior: `read` takes a slice of bytes, returns an int and an error. But let me ask you a question before we proceed. Could I have defined `read` differently? For example, what if I passed in the number of bytes I want to read and returned the slice of bytes? Many might argue this is a simpler API. But why is this design horrific? Why is line 13 such a poor API design choice in Go?

Because if we write code like line 13, every time we call `read`, we’ll be allocating on the heap. Remember, in API design, you have two responsibilities: first, to write precise APIs that prevent misuse; second, to minimize the impact your APIs have on the user’s machine. Here, the impact is allocation.

You might ask, “Bill, where is the allocation coming from?” You can’t implement methods like this directly on the type—I’m just sketching this to show where allocations would occur. The first allocation comes from having to create a slice of bytes based on `n`. Since `n` is not known at compile time, the backing array size is unknown—so we must allocate. That’s one potential allocation.

But you might say, “Bill, I won’t let you pass `n`. I’ll hardcode it.” Okay, now the compiler knows the backing array size—so that allocation is avoided. But guess what? Eventually, you still have to return the slice up the call stack. That will cause an allocation because you can’t have a pointer pointing down the call stack. Do you see why this API design is flawed?

If this API is used in a tight loop, the allocation overhead becomes significant. The alternative API I’ve chosen avoids allocation because it asks the caller to allocate the memory and share it down. In Go, you have significant control over memory management because you understand escape analysis. You can pass data down the call stack—meaning the caller can define the slice, even with a hardcoded size, and share it. No allocation occurs.

I bring this up to highlight where allocations happen and how escape analysis plays a role. This was one such case.

Now, back to the `reader` interface type. I can declare variables of this type. But remember: `r` is not real. Interfaces are not real. Internalize this: only concrete data is real. On line 15, I define a concrete type named `file`. This `file` represents a concrete piece of data—perhaps a file on a filesystem—and it contains just a name.

More importantly, look at line 20. There, I declare a method named `read` with the same signature as the interface: takes a slice of bytes, returns an int and an error. Go is about convention over configuration. Listen carefully—every word matters. Because of this method declaration on line 20, I can now say: the concrete type `file` implements the `reader` interface using value semantics.

Think about that. Every word is important. Because of line 20, the concrete type `file` now implements the `reader` interface using value semantics. Go uses convention, not configuration. We don’t explicitly configure an interface to a concrete type, as you might in other languages. In other languages, you might write something like `class File implements Reader`—that’s configuration. That limits us and leads to more code.

Go aims for fewer lines of code and greater productivity. Thanks to static code analysis, we gain significant benefits, which I’ll show as we move from mechanics to design. In Go, we don’t need configuration. We just declare the method as on line 20, and the compiler can verify interface compliance at compile time.

So now, the concrete data associated with the concrete type `file`—notice I keep saying “concrete”—implements the `reader` interface using value semantics. Again: the concrete type `file` implements the `reader` interface using value semantics. The implementation itself is trivial—we’re just pretending to read a file.

Now look at the second concrete type: `pipe`, meant to represent a networking pipe. On line 32, it also has a method named `read` with the same signature—part of the `reader` interface. Since the `reader` interface has only one method, this satisfies the entire method set.

Therefore, because of line 32, I can now say: the concrete type `pipe` implements the `reader` interface using value semantics. I now have two distinct pieces of concrete data—two concrete types—each with their own implementation of the `reader` interface. This sets us up perfectly for polymorphism.

Recall the definition: polymorphism means a piece of code changes its behavior depending on the concrete data it operates on. I love that definition—it gives me chills—because it emphasizes data-oriented design and the primacy of concrete data in driving everything we do, even decoupling.

Now, let’s look at what I call our polymorphic function: `retrieve`, on line 50. This function is polymorphic and contains no knowledge of concrete types. In fact, you might be momentarily confused by its parameter. It appears to say: “Pass me a value of type `reader`.” But we already know that’s impossible.

There are no values of type `reader`. Interface types are valueless. So how can this function ask for a value that doesn’t exist? What is it really saying?

Actually, `retrieve` is saying: “Please pass me any piece of concrete data—any value or pointer—that satisfies the `reader` interface, meaning it implements the full method set of `reader`.” Remember, we can only pass concrete data across program boundaries. That’s what’s real. That’s what we manipulate. That’s what solves problems.

So this function is asking: “Pass me any concrete data—value or pointer—that implements the `reader` interface, fulfilling this behavioral contract.” And don’t we already have two such concrete types in this program? Yes: `file` and `pipe`, both of which implement the full method set of `reader`.

This is why `retrieve` is polymorphic: it knows nothing about the concrete types. It can operate on any concrete data that implements the `read` method. And you can see it executes that behavior through the interface.

Let’s visualize the mechanics on the board to see polymorphism in action—both the semantics and the mechanics.

Look at line 41. What are we doing? We’re creating a value of type `file`—that’s `f`. We’re also creating a value of type `pipe`—that’s `p`. We’re constructing these to variables using value semantics. Great.

Now look at line 45. We call `retrieve`. I always ask the same question: what semantics are in play? Is the function receiving its own copy of the data (value semantics), or is it sharing the data (pointer semantics)?

We see that `retrieve` is being passed a copy of `f`. So we’re passing a copy of the file value across the program boundary. The compiler, at compile time, knows this data implements the `reader` interface using value semantics—verified via static analysis.

Remember: `retrieve` says, “Pass me any concrete data—value or pointer—that satisfies the `reader` interface.” We know `f` does. We know it has a `read` method. Perfect.

But on the other side, even though we’re passing a copy, what we receive is `r`—an interface value. From our programming model, `r` is not real. It is valueless. There’s nothing concrete about it. But from an implementation perspective, `r` is a reference type—an interface value consisting of two pointers.

When `r` is zero-valued, both pointers are `nil`. But when we pass concrete data across the boundary, we establish a relationship: interface values store concrete data. When we store concrete data inside an interface value, that makes the interface value concrete. Everything must return to the concrete.

So even though interfaces are valueless, through storage, they become meaningful. We made a copy of `f` and stored it inside the interface. This gives us decoupling.

But making a copy means allocation. Every time I draw that little red mark, it means allocation. So yes, there’s an allocation.

Now, what about the first word of the interface value? We’re getting a bit technical. The first word points to a special internal table called the iTable. If you’re familiar with vtables in object-oriented languages, an iTable is similar—but for interfaces, so “v” becomes “i”.

Vtables are matrices of function pointers that allow base class pointers to invoke derived class behavior. The iTable does something similar. The first word of the iTable describes the type of the stored value—in this case, `file`. The rest of the iTable contains function pointers—in this case, pointing to the concrete `read` method for `file`.

So on line 53, when we call `read` on the interface, we perform an iTable lookup. This is indirection: we look up where the actual `read` implementation is, then call it on our copy of the data. So we have indirection and allocation—this is the cost of decoupling.

But the benefit? We have a polymorphic function that can handle any concrete data. And `retrieve` changes its behavior: the call to `read` behaves differently depending on the concrete data it operates on. Right now, it’s accessing a file system. How cool is that?

Now look at line 46: we call `retrieve` again. What semantics are in play? Value semantics again. This time, `retrieve` receives a copy of `p`, the pipe value. We know `p` also implements `read`.

This time, the iTable doesn’t say `file`—it says `pipe`. Brilliant. Now, when we call `read` on `r`, it calls the `read` method on our copy of `p`. Another allocation—but the behavior has changed.

Polymorphism means a piece of code like `retrieve` changes its behavior depending on the concrete data it operates on. It’s all driven by real, concrete data. This makes the interface value concrete. But ultimately, interface values are valueless. They’re not real. Only the concrete data is real.

Moving forward, I don’t focus much on iTables—they’re too technical. But I want you to know they exist and that the first word is a pointer. Going forward, I’ll just write the type of the stored value in that word—like “pipe”—to indicate we’re using value semantics. This means we have a copy.

When we call `read` on the interface, the iTable handles the dispatch. There’s no magic here.

This is basic polymorphism in Go. It shows when data should have behavior: when we need polymorphism to achieve decoupling. But I must stress: decoupling has a cost. That cost is indirection and allocation.