So we just looked at our value semantics example, but let's imagine that what we really wanted is not the goroutine operating in its own sandbox with its own copy. Let's say that the next data transformation needed to manipulate the memory located in the frame above us. In this case, we need pointer semantics. Pointer semantics serve one purpose: to share a piece of data across a program boundary. Sharing is the key—if you don't need to share something, then you don't need a pointer, and you don't need pointer semantics. This is where pointers become essential.

Let's examine the mechanics of sharing, then discuss the benefits and costs of pointer semantics, and continue integrating these concepts as we progress. Let's return to our original program. We'll reset the stack—we're back in main, operating on the stack. <PERSON> has a variable called count. We can get the value of count, and we can get the address of count. But this time, on line 17, we've done something different: we're not passing the value of count across the program boundary. Instead, we're passing the address of count.

You might think this is called "pass by reference," and many people refer to it that way, but it's not accurate. Remember, "pass by value" means WYSIWYG—what you see is what you get. Pass by value means we make copies of data as we cross program boundaries. This is still pass by value. The difference is that the data being copied and passed is not the value itself, but the address of the value. I need to emphasize: addresses are data. Since we're making a copy of count's address and passing it across the boundary, we need a way to store that address. That's exactly what pointer variables are for.

Pointer variables aren't special—they serve one purpose: to store addresses, which are a form of data. If you look at the parameter declaration for the increment function, you'll see we've added the star operator (*). This operator allows us to declare a pointer variable. It's important to understand that pointers are literal types. As we discussed earlier, Go has named types, literal types, and unnamed types. Pointers fall into the category of literal types. You can take any existing type—like the named type int—and place a star in front of it to create the literal pointer type for that type.

This changes everything: inc is no longer representing an integer value; it now represents the ability to store an address. That's what we're doing—we're storing the address of count and passing it across the program boundary. That address is what will be stored in this variable's box. As a result, we can now point to data outside of our current frame. Pointers are for sharing.

Pointers can be confusing because of the star operator. The star dictates that this is a pointer variable. A pointer variable always occupies four to eight bytes of memory, depending on the system architecture—the size of a memory address. That's what we're storing: an address. But here's the critical part: you might think, "If the star determines the variable's type, why can't we just say *? After all, the star is what makes it a pointer and determines the size." You're right—except for one thing. It's not enough to just store an address. The entire reason we store an address is so we can read from or write to the memory that the address refers to—in this case, the count variable.

As I've said before, you cannot read or write memory without understanding its type. You must know the size and representation of the data. Type is life. So we can't just say *; we must say *int. What I'm saying is: I need an address—absolutely, no hesitation—but it can't be just any address. It must be an address to an integer, because I want to read and write integers. This gives us type safety: the compiler will only allow addresses to values based on the integer type. That's the guarantee.

Then, on line 28, we see the star operator used again. This can be confusing. Remember, during type declaration, the star defines the variable as a pointer. But when used in an expression, the star means something different. Let's express this in plain terms: using a variable by itself means "the value of what's in the box." Using &variable means "the address of, where is the box?" And if you have a pointer variable, then *pointer means "the value that the pointer points to." This is called an indirect memory read or write.

What we see on line 28 is an indirect memory read, modify, and write operation—achieved through pointer indirection. Recall that a goroutine can only directly read and write memory within its own frame. When we make this function call, the current frame is no longer active; the new function's frame becomes active. The goroutine operates within this new sandbox. Even though this is now the active frame, the goroutine still performs a direct memory read of the pointer variable—because that variable is within its frame.

But to access memory outside its frame, it must use the stored address to perform an indirect access. The goroutine only has direct memory access to its own frame. To access memory outside the frame, we must share the address of that memory location in a safe way. Line 28 performs an indirect memory read, modify, and write. This allows us to modify memory located outside our current frame. The pointer gives us this capability.

However, there is a significant cost to using pointers. We've now given up mutation isolation and immutability. We've set ourselves up for what are known as side effects in our code. When we mutate memory, we must be extremely careful. If we mutate memory within the scope of value semantics, we're safe—those changes are contained and can't affect other parts of the program. But when we use pointer semantics to mutate memory, we must be cautious. At some point, we will return from this function, exit this program boundary, and return to the calling context.

When we come back, we may not realize that the goroutine—or another goroutine in a multi-threaded program—has modified this data behind the scenes. The data is no longer clean; it's been altered. If we're unaware of this mutation, it can lead to serious side effects. In a multi-threaded environment, it could even result in data races.

Functional programming languages attempt to minimize side effects by eliminating pointer semantics entirely—everything uses value semantics. But remember, the cost of value semantics is inefficiency with data, due to copying. There are situations where it's simply more efficient and practical to have a single piece of data and share it across the program using pointers.

Throughout this course, we'll continue discussing when to use value semantics and when to use pointer semantics—I promise. Right now, we're focused on the mechanics and foundational concepts. But as we progress, we'll build the knowledge to help you decide when to use one semantic over the other. There's still more to cover.

When I run this program, you'll see that upon return, the memory in the frame above us has been mutated. When we come back up, we observe that change. This is technically a side effect: we've modified memory outside our isolated, safe space. This requires careful handling.

Now, there's one more point I want to make regarding what we've seen. I've been drawing the active frame for you. It's crucial to understand that any memory located below the active frame is not valid. None of this memory is valid. You may have noticed we've been moving the stack downward—that's just a historical implementation detail; it could have been designed differently. But the key idea is that as we make function calls, we go deeper into the stack, and as we return, we come back up.

Stack and frame pointers help us track where the active frame is. What's important is that anything below the active frame is no longer valid—it's out of scope and no longer in play. Memory in the active frame and above maintains integrity because we can safely return to those frames, but going downward introduces risk.

Remember the concept of the zero value—it's a powerful feature in Go. Right now, I'm back in main, and we've performed this mutation. But what happens when main makes another function call? It will need a new frame, and when it allocates that frame, it will be cleaned—typically set to its zero value. This is why I stress that memory below the active frame has no integrity: it will be reused. Keep this in mind as we continue learning about value and pointer semantics in the context of the stack.