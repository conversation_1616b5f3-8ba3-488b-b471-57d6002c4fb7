Behavior as context allows us to use custom error types while staying decoupled. Let me show you how. Let's start again with our type-as-context example. In this function—specifically, this method—I'm making a call to `readString` based on an interface value called `reader`. We can imagine this abstracts a network call. When we make this call over the network, we should either get a line of data back or encounter an error. So we check whether there's a concrete value stored inside the error interface. If there is, notice what I'm doing: I'm using type as context to check the different types of concrete error values defined in the `net` package—pointers to `OpError`, pointers to `AddrError`, pointers to `DNSConfigError`. There are many types of errors that can occur in networking, and the `net` package attempts to cover them all.

This type-as-context approach looks powerful because it allows us to filter based on which specific error was returned through the error interface. You can see I'm calling the `Temporary` method on each of these concrete values because we're told that if such a method exists, we should use it. Remember, errors in Go are just values—they can carry both state and behavior. The `Temporary` method is particularly valuable: if an error is temporary, we know we're still in a state of integrity and can continue retrying. If it's not temporary, integrity has been lost—perhaps the listener has gone down or the socket has dropped—and we must take steps to recover.

This is useful, but there's a problem: we're moving from a decoupled to a coupled state. If we make changes to any of these concrete error types, this code will break. This is especially risky when dealing with types from the `net` package, which is used by countless applications. Such changes could trigger cascading breakages across large codebases we don't even control.

To avoid this, we want to maintain decoupling. Let me switch to the `net` package for a moment. I'll search for `golang/net` and look up `OpError`, which is the most common concrete error type used in the `net` package for error handling. Notice something I didn't mention earlier: there's a naming convention for custom error types—they end in the word "Error." You see it here with `OpError`, `AddrError`, `DNSConfigError`. This is a convention you should follow when defining your own custom error types.

Now, focusing on `OpError`—around line 414—you can see it's an exported type with five exported fields. The `net` package exposes all the details of the networking error, allowing full inspection. But if I scroll down, you'll see the implementation of the `error` interface. It uses pointer semantics, which isn't surprising, and the `Error` method's string formatting is quite complex. We definitely don't want to parse that string to understand the error.

All these error types implement the `error` interface with pointer semantics, but the key method I want to highlight is `Temporary`. Let's find the `Temporary` method for `OpError`. Look at how complex it is—full of type assertions. Thank goodness the standard library implemented this; I wouldn't have figured it out myself. The fact that `Temporary` returns a boolean is brilliant and critically important. It allows us to simplify the decision of whether we have an integrity issue in the face of complex networking problems.

Now, going back to our original code, notice the pattern: we're type-asserting to a concrete type just to call `Temporary`, then doing it again for another type, and again. The common behavior across all of them is the `Temporary` method. That's really all I care about. Technically, I don't even care what the concrete type is—as long as it has a `Temporary` method.

Let's apply the principle of behavior as context to clean this up. Look at this: the `net` package actually defines an interface called `temporary`—but it's unexported. That means we can't access it from our code. It would have been convenient to use it for our own decoupling, but exporting it would have been a mistake. Why? Because only code within the `net` package needs to implement this behavior. They own the custom error types and must be coupled internally. We're not implementing `Temporary` on their behalf—it's for internal use.

But remember, Go favors convention over configuration. Nothing stops us from defining our own `temporary` interface. And isn't it true that any concrete type in the `net` package that has a `Temporary` method already satisfies our interface? Absolutely. That's the beauty of Go's static type system and structural typing. We can define an interface that the existing values—whether data or pointers—already implement.

So here's what we do: instead of handling three separate cases where we type-assert to concrete types just to call `Temporary`, we collapse them into one. Now, when we do the type assertion, we're not asking what the concrete type is—we're asking: does this value implement our `temporary` interface? We stay decoupled. We don't care about the concrete type anymore. All we care about is whether it implements `Temporary`, which tells us whether we have an integrity issue.

This is powerful. The fact that error values can have behavior—not just state—allows callers to maintain decoupling in error handling, even when using custom error types.

Here’s a rule I want you to follow: if your custom error type can implement any one of these four methods—`Temporary`, `Timeout`, `NotFound`, or `NotAuthorized`—then define the custom error type as unexported with unexported fields. By doing this, you prevent users from ever type-asserting to the concrete type. They can't break decoupling. You're helping them, even if it feels restrictive.

Why these four? Because they represent common behavioral checks that allow decoupled error handling. `Temporary` is especially powerful—it's a broad indicator of whether an error is transient or permanent. If someone complains that you've made the error type unexported but you have a valid `Temporary` method, tell them: we're not exporting the type, even if you feel the need to inspect it directly. The benefit is that we can improve the `Temporary` method's accuracy without causing cascading changes. The calling code doesn't break—it only gets better as the implementation becomes more precise.

Now, there are exceptions. Consider the `json` package: its error types don't fit this model. Methods like `Temporary`, `Timeout`, `NotFound`, or `NotAuthorized` don't apply. Those error types must be exported because users may need to inspect them directly and type-assert to the concrete type. It's a less ideal, more coupled situation, but sometimes necessary.

Most of the time, though, the `Temporary` method is sufficient. I don't mind using custom error types once simple error variables no longer suffice—but I strongly prefer that those custom types be unexported with unexported fields, and that we provide behavioral method sets users can rely on. This ensures error handling remains decoupled and maintainable.