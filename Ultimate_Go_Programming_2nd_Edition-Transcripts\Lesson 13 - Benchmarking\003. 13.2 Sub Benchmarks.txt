So the cool thing here—and also in benchmarks—is that we're able to create sub-benchmarks. If you remember from the testing section, which I hope you saw, we had the concept of subtests, where we were able to take data-driven tests, give them names, run them individually, or run them in parallel. We have the same concept here in Go with our benchmarks. I want to show you a quick example of a sub-benchmark and how we can leverage that as well.

I've got that global variable again, GS. Remember, I want that code to be thrown out, and we're going to be using our Sprintf and Sprintln functions again. But notice what I'm doing in the benchmark for Sprintf here. I've actually created an unexported function on line 26 specifically for our benchmarks. Line 26 now defines the unexported benchSprint and the unexported benchSprintf, both taking a testing.B pointer. And we're going to use named functions—not literals—when calling B.Run. It's the same exact API, just using B instead of T. We've given each sub-benchmark a name, so while this isn't strictly data-driven like our tests, what we gain is the ability to filter—filter based on the sub-name: "none" or "format," depending on what we want to do.

So here we have Sprintf with "none," where we're not doing any formatting. Then we give it the name "format." Now I can run these benchmarks as well. I'm already in the command line, so let's run `go test` again—hey, don't run anything that might be called "test," let's get all that out of the way. Let's do `-bench=.` to run all benchmarks, change the benchtime to something like three seconds, and we can still look at memory for this test. Throwing in a few extra flags is no big deal—we'll get it.

And we can see again that we're getting the same output: 68 nanoseconds per operation. We ran it a million times, 50 million, even up to a hundred million—it's all right there. Let's run it again, just so we can see it a little bit cleaner. You can see how the sub-name is now part of the output, making it much cleaner. We see "Sprint" and then "none," and then "format."

Because I now have these sub-names, I can do something like: let's run Sprintf, and just focus on "none." Now it runs only Sprintf/none, so I'm able to focus on just one of those benchmarks instead of both. This is at the sub-benchmark level.

Now, I would be very careful about running sub-benchmarks in parallel. The reason is you have to make sure your machine is idle when doing performance measurements. I'm going to come back to this very soon when we start talking about profiling—I'll be very focused on what state your machine should be in. But just as a note right now: when you're running these types of performance benchmarks, especially CPU-based ones, the machine must be idle. You can't be browsing the internet or running other applications, because those consume CPU cycles on the hardware. That interference means you're not getting accurate results, so it's generally not advisable to run benchmarks in parallel. We still want to run them in series. Sub-benchmarks just give us a little extra granularity in how we organize and filter the benchmarks we're creating.