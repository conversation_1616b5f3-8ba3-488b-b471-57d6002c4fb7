So now we're going to look at the base pattern around a fan-out. Fan-outs allow you to take a piece of work and distribute it across *n* number of goroutines that can run in parallel. We're going to use the "wait for result" pattern as our foundation to build this higher-level pattern. But I need to stress something very quickly: fan-out patterns are dangerous, especially in web services where you might already have 10,000 goroutines running in the service, and suddenly one goroutine fans out another *n* number of goroutines. These things can multiply very quickly. That's why I'm cautious about fan-out patterns in long-running applications like services. However, for background applications that run on cron jobs at certain intervals, or for CLI tooling, they're excellent.

I once had someone from Cisco come up to me, really excited, saying, "<PERSON>, Go is amazing—you've gotta see what I did. I made some software run incredibly fast by using a fan-out pattern." I asked, "What are you talking about?" He said, "Bill, I've got this web service, and when a request comes in, we know that's an independent path of execution—it's a goroutine. And <PERSON>, I had to hit a MongoDB database. In the process of handling this request, I needed to perform 500 independent database operations. So <PERSON>, I went ahead and created 500 goroutines that went out and hit the database—all of them independently doing their thing. Man, <PERSON>, you should've seen how fast that work got done. Go is amazing."

I started to cringe. I asked him, "How many requests did you run through the server when you launched these 500 goroutines?" Because there's a one-to-500 ratio here. He said, "<PERSON>, <PERSON>, just one or two—you should've seen how fast it was." I replied, "Well, what happens when you have 10,000 requests, each spawning 500 goroutines?" That's a massive number of goroutines. Plus, you're not going to be able to get that many connections to your MongoDB database. What you're doing is putting an enormous amount of load on the scheduler and introducing huge amounts of unknown latency just to get resource access." He got very sad. I told him, "Look, it's okay, but you've got to ask yourself: did we get those 500 transactions done fast enough on a single goroutine? Because if we did, now we can scale."

You have to think about scale when it comes to fan-out patterns—that's why they scare me. So be very careful when you're fanning things out, especially in environments where there could be hundreds of thousands—or tens of thousands—of goroutines, all wanting to fan out at the same time.

Now, notice on lines 126 and 127: we are now using a buffered channel with a buffer size of 20. Any time we use a buffered channel, the buffer size must not be arbitrary—it has to represent something measured or real. In this case, we're launching 20 goroutines in our fan-out, so the number 20 represents one buffer slot for every goroutine we're going to fan out. That's a concrete, measurable number. It's not "let me use 10,000" just because it sounds big. No, no, no, no, no, no. Any time I see a buffered channel with a number that doesn't seem reasonable, practical, or tied to something real, we have to stop and ask: what are we doing?

I want you to remember: buffers don't provide performance. Don't use them under the assumption that you're reducing latency between sends and receives and therefore gaining performance. When using buffered channels, one of the key questions we must ask is: can the sending goroutine on this channel ever block? In other words, can the buffer ever fill up? In a fan-out pattern like this, we size the buffer so that no send operation will ever block—because we have a one-to-one correspondence between goroutines and buffer slots. But any time a buffered channel *can* fill up, we must ask: what happens when a send blocks? If you can't answer that, you've put your server in a very bad place.

I've seen too much code where someone says, "I'm going to use a buffer of 10,000 because I don't want any send to ever block"—maybe they're pulling data off a network. Eventually, the system grows large enough that 10,000 isn't enough. It never really was enough. Maybe they keep increasing the buffer size, but they're only causing themselves pain because they're not achieving real orchestration—they're guessing they have enough capacity. We cannot guess. There are no magic numbers. You can't guess.

So we use a buffer slot for every goroutine we create. Remember: that's the key—a buffer slot for every goroutine. That's what makes it measurable.

Here we are—this is us, this is our goroutine, our manager. We get to line 129, and there we launch 20 goroutines in a loop. This is still the "wait for result" pattern. We're launching 20 goroutines at this point, and each one knows what it needs to do. On line 131, we simulate the work these goroutines must perform—each knows its task.

We've set up a buffered channel of size 20, which means that when any of these goroutines finish their work and reach line 132—the signaling with data—each has its own dedicated slot in the buffer. So if one finishes first, it sends immediately and the value goes right into the buffer with no latency between send and receive. At this point, we are blocked in a receive operation on line 138. Our goal is to pull data out as soon as it's placed into the buffer.

Because the send happens before the receive, there's no latency between them. If the next goroutine finishes, it sends immediately—boom. The next one—boom. Another one—boom. At the same time, we're pulling values out one by one. We know that each send and receive pair has no real latency.

However, two goroutines could finish at the same time. If two finish simultaneously, there will be blocking latency between their sends because only one send can occur at a time. So in this pattern, if we looked at a blocking profile, we'd likely see more latency on the send side than the receive side—and that latency isn't between send and receive, but between multiple sends competing to write to the channel.

You can picture all these goroutines going off to do their database work, then sending back their results. We have a local counter that acts like a local wait group. We sit here and loop 20 times, performing a receive each time. When we get a piece of data, we decrement the work count.

How long we wait here is still unknown—this is still unknown latency. Even though we're using a buffered channel, we don't have timing guarantees. But we do have orchestration guarantees: we won't proceed until all the data comes back. We won't return from main until the wait group reaches zero. We won't move on until we've received all 20 results. There's no guarantee about which goroutine finishes first or when they all complete—but we *will* get all 20 back.

This is a classic fan-out pattern—one we can use safely when it's appropriate to launch multiple goroutines for a given task. Again, I recommend avoiding this in web services, but for CLI programs, background jobs, cron-based tasks, or Lambda functions written in Go, it's a very powerful pattern.