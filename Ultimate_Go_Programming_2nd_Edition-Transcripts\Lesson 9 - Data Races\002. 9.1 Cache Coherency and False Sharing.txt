Okay, we're now about to talk about data races. A data race occurs when you have at least two paths of execution—like two Go routines—accessing the same memory location at the same time, where one is performing a read and the other is at least performing a write. Both could be doing writes as well, and that would still constitute a data race. And that is bad. You cannot be mutating memory while another path of execution is reading or writing to the same location. With two or more paths accessing the same memory concurrently, you will end up with data corruption. This is where synchronization comes in.

There are two distinct problems here: synchronization and orchestration. We're focusing on synchronization in this section about data races. The best way to think about synchronization is to imagine going to Starbucks and getting in line because you want coffee. While you're waiting in line for your turn at the counter, that's a synchronization issue—Go routines getting in line is a synchronization problem. But once you reach the counter and start interacting with the barista, exchanging information and money, that's an orchestration issue. There's a workflow, a conversation, data moving back and forth. That's orchestration. You need to understand when you're dealing with synchronization—when things need to get in line—and when you're dealing with orchestration—when there's a coordinated workflow.

Again, a data race happens when two or more Go routines access the same memory location concurrently, with at least one performing a write and another a read. This is extremely dangerous and must be avoided. But there are also important hardware-level considerations that affect how we write multi-threaded software. At the hardware level, we operate with value semantics—meaning each core works on its own copy of data. This is crucial to understand because it can significantly impact performance. While caching systems help reduce latency when accessing main memory, they can also lead to memory thrashing if we're not careful.

I strongly hope you've gone back and watched the earlier videos, especially the sections on arrays and data registration, where we discuss cache lines in detail. That knowledge is essential before diving into concurrency. I'm going to assume you're familiar with how arrays and cache lines work as we move forward.

Now, let's discuss the cache coherency problem and how value semantics at the hardware level can negatively impact performance. Then we'll explore an even more subtle issue: false sharing, which is closely related to cache coherency.

Consider a four-core processor: core one, two, three, and four. Each core has its own L1 and L2 cache, with a shared L3 cache. I mentioned that hardware operates with value semantics because each core works on its own copy of data. Imagine we have a global variable called `counter`, initialized to zero. We launch four Go routines—Go routine zero, one, two, and three—each running in parallel on its own P, M, and core.

If all four Go routines attempt to read, modify, and write the same variable `C`, we have a synchronization problem. We must ensure that only one Go routine performs this operation at a time. This is where atomic instructions—hardware-level operations—and mutexes—slightly higher-level constructs—come into play. I'll soon show you how to use atomics and mutexes to make operations atomic, even across multiple statements.

But let's return to the hardware. Remember this: if each Go routine needs to read, modify, and write `C`, a copy of `C` must be brought into each core's cache. Specifically, the entire cache line containing `C` is loaded into each core's cache.

Here's where it gets interesting. Suppose we use atomic instructions to synchronize access to `C`, since it's a counter. When Go routine zero on core one performs a read-modify-write to increment `C` from zero to one, the hardware ensures exclusivity. Through mechanisms like cache coherency protocols, the other cores (G1, G2, G3) are put on hold while G0 completes its operation.

But now consider the memory impact. When G0 increments the value, the cache line is marked as dirty. The updated value may eventually propagate back to main memory, but the key point is that the other cores now hold stale, dirty copies of that cache line. When Go routine one gets its turn—through proper synchronization—it detects that its local cache line is dirty and no longer valid. It must then fetch the updated value from main memory, incurring a significant latency cost—potentially 107 clock cycles.

After G1 increments the value from one to two, it marks its cache line dirty again, invalidating the copies in the other cores. This cycle repeats with every increment. Even though synchronization is working correctly, we're constantly invalidating and reloading cache lines across all cores. This thrashing effect becomes worse with more cores—on a 36-core processor, or across multiple physical processors, the inter-core communication overhead grows substantially.

This is why you must be extremely careful with global variables and global counters in multi-threaded programs. You're not just updating a single value—you're triggering widespread cache invalidations due to the hardware's value semantics, which are designed to minimize main memory access but can backfire when data is shared across cores.

Now, there's another related issue called false sharing, which is particularly insidious. False sharing occurs when you don't have a synchronization problem—no data race—but you still suffer from cache coherency overhead.

Imagine we decide to avoid the shared counter problem. Instead of having all Go routines increment the same global variable, we give each one its own counter. Let's say we have an array of counters: counter[0], counter[1], counter[2], counter[3]. Each Go routine increments its own index: G0 increments counter[0], G1 increments counter[1], and so on.

Now there's no synchronization issue. The memory addresses are independent. There's no data race because no two Go routines are accessing the same memory location. You don't need atomic instructions or mutexes. When G0 increments its counter from zero to one, it shouldn't affect G1 doing the same to its counter.

However, remember the hardware's value semantics and cache behavior. Even though the addresses are different, the counters are likely stored in the same cache line—a 64-byte block of memory. When the array is loaded, the entire cache line containing all four counters is duplicated across each core's cache.

So when G0 performs a read-modify-write on counter[0], it marks the entire cache line as dirty. This invalidates the corresponding cache lines in all other cores—even though they're accessing different counters. When G1 then tries to increment counter[1], it finds its cache line is dirty and must reload it from main memory. Every single increment, even on logically independent data, triggers this cache line invalidation and reload.

The result? Severe memory thrashing—exactly like in the shared counter case—even though there's no actual data race. This is false sharing: data that is logically independent but physically adjacent ends up sharing a cache line, causing unnecessary coherency traffic.

Multi-threaded software is complicated. Especially when your data elements are stored close together in memory, even if they're accessed independently. False sharing arises from data access patterns to adjacent memory locations that happen to reside on the same cache line. In effect, you're still sharing the cache line, even if you're not sharing the data.

As we continue learning about data races, synchronization, and orchestration—and as we move into tooling and live coding—I'll revisit these concepts. Any time you use a global variable, you must consider synchronization: with multiple execution paths, you cannot allow concurrent reads and writes to the same memory. But you must also consider data layout and access patterns, even when data is unique. You want to avoid both false sharing and broader cache coherency problems that result in constant memory thrashing due to repeated modifications of data copies across cores.

We'll keep these issues in mind throughout the course. One of our goals in multi-threaded programming is to achieve linear performance scaling as the number of cores increases. If your performance curve flattens or degrades as you add cores, it's a sign you're not being mechanically sympathetic. It could be due to cache coherency issues, memory thrashing, or false sharing. Recognizing and addressing these problems is essential.

The first thing we'll do is examine a simple data race and explore ways to correct it, ensuring proper synchronization and avoiding these low-level performance pitfalls.