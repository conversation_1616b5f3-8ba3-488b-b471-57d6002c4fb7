All right, before we get into project structure, I think it's really important to have a philosophy behind what we're doing because a philosophy allows you to validate what you're doing. I was on the airplane on my way in to record this, and I was writing some code. In my head, I kept thinking, "This API design is violating one of my philosophies." I kept thinking this over and over again. The guy sitting next to me turned around and said, "Did you say something?" I looked at him and said, "What?" He repeated, "Did you say something?" I asked, "Did you hear me say 'that I'm violating a philosophy'?" He said, "Yeah," and I started laughing. It was so ingrained in my head that I actually spoke it out loud on the plane. I think guidelines and philosophies are very important because they allow you to make sure you're making the right engineering choices and not going off the deep end.

When it comes to packaging design philosophy, three things are very important: purpose, usability, and portability. I believe the Go community broadly supports these philosophies, though we may implement them in different ways. The first is purpose. A package must have purpose. For a package to be purposeful, it must provide and not contain. Packages cannot be dumping grounds for generic code. Every package must have a clear API that defines what it provides to the user. If that isn't clear, you should question the package's existence. The package name should describe the intent of what it provides. If you're struggling to name a package, that's likely a code smell.

Packages like net, fmt, http, and os are clear—we know what they provide. But packages like util, helper, or common are containers for code, and they cause problems. They become dumping grounds, single points of dependency, and eventually lead to collapse. I promise you this: if you have a package today called models, or any package that's just a common set of types, your project has already failed. In monolithic applications, you might get away with that kind of encapsulation from a type perspective, but not in Go. Types are artifacts to move data across program boundaries. They cannot be an API in and of themselves. Avoid packages like models.

Every purposeful package—every package with real purpose—has its own type system. Even if you have to duplicate types across multiple packages, that's better. Remember, concrete data solves the problem. We can later leverage interfaces to decouple our APIs and accept anyone's concrete data. You're not limited to your own concrete types through an interface—you can use anyone's. Avoid packages like models because they represent a large container, and changing one model or one concrete type can trigger cascading changes throughout your codebase. That's very dangerous. A package must provide and not contain. There are very few exceptions to this rule.

You don't have to dive deep into usability—these are principles we've discussed before: intuitive design, simplicity, respecting resource and performance impact, minimizing cascading changes for users, avoiding assertions to concrete types. We always want to reduce, minimize, and simplify our codebase. Usability is more of an art. It also means designing APIs and packages to reduce fraud and misuse. Precision is everything. We don't want vague packages or APIs. New semantics should add precision to reduce misuse and fraud.

Portability is usually what most developers think about when discussing packaging. The more decoupled and reusable a package is, the better it will be in your ecosystem. We want to be able to pick up packages and move them. We must minimize coupling because coupling creates constraints. Policy is a big part of this too. When I say policy, I mean decisions a package makes about logging, configuration, or other behaviors. If a package enforces such decisions, only applications that want to do things the same way can use it. The more policy a package has, the less reusable it becomes. We need to be clear about when we're setting policy, and the purpose of the package should guide those decisions.

Again, a single point of dependency leads to failure over time. That's why containing packages are so dangerous—especially one called models—because everything binds to it. Eventually, the entire system collapses. We need to leverage these design philosophies as we discuss different project structures. I'm going to share a project structure with you that is my own, that works, that aligns with these design philosophies, and enables clear engineering decisions across teams—giving us the consistency we need.