Let's really look at how composition works in Go with this example, and I want to give you some guidelines around doing this and writing code in Go. It's probably a little bit against what you're used to doing. A lot of you have been taught to start with the interface, start with the behavior, try to figure out what those contracts are. I don't want you to do that. That is guessing. I don't want you to do that. Remember, the problem is solved in the concrete, not in the abstract. So we need a concrete, implementation-first solution in order to know how to decouple. This is going to simplify your code and allow you to focus on what's important, which is the problem—and remember, the problem is the data.

There are a couple of things I also want to talk about before we get into this example—things that I see are missing. One of these is the idea of "done." How do you know when you're done with a piece of code? And if you manage people, how do you know that they are done? This is a really big, important question. For me, "done" comes in two parts. The first is that you must have your unit tests and some level of test coverage. For me, I want to see anywhere from 70 to 80 percent code coverage on tests before you can come to me and say you're done. It's really hard to get above that sometimes in Go because we focus so much on error handling, and 100 percent code coverage doesn't necessarily add value—it just means you've executed every line of code once. I would like 100 percent code coverage on the happy path, but overall, 70 to 80 percent is good enough for me.

The other part of "done" is whether the code is decoupled from the changes we expect to happen. Now, we might decide that we know what decoupling is required, but we don't need it today because we don't have multiple implementations of something. We don't have to need it today. Remember, you're writing code for today, but you're designing and architecting for tomorrow. So deciding to decouple may or may not happen immediately. But I want to ask the question: Do we know what has to be decoupled, and do we want to do that? I can ask this because decoupling is part two of everything I do. It is a refactoring. We solve problems in the concrete first, then refactor into decoupling.

There are a couple of other things I see developers struggle with. One is breaking problems down. I'm not talking about tasks—GitHub tasks or Jira tasks. I'm talking about looking at a problem and saying, "I've got to solve this, and I've got to solve that, and I've got to solve this, and it has to be in this order, and if I don't solve this, the other problems don't matter." These can be small or large, and each might involve multiple tasks. We're going to go through that exercise as well.

One of the very last things I see quite a bit is that software developers don't really understand how to create a layered API. This is going to help you with your codebase instead of trying to solve every problem in a few functions. What we really want to do is find a layered approach. Initially, just three layers are all we're going to need.

We can have what I call our Primitive layer. This layer knows how to do one thing, and one thing very, very well. We're always focused on what that one thing is. We write the code for this layer, we write our unit tests for this layer, and we write this layer so it is testable. When I say testable, this doesn't mean it has interfaces. Remember, I'm working in the concrete—interfaces and decoupling come later through refactoring. Yet I still have to start writing unit tests. Unit tests and testable code usually mean that the data we're passing in is reproducible, and the data coming out is testable. So I'm always going to be telling you, over and over again: testability is about the data. The code we're writing is about the data. Decoupling is about the data. The problem you're trying to solve is about the data.

When we think about unit testing here, I want you to think about whether you can write a function in the primitive layer where you can give it a set of data, get a set of data back, and validate and test it.

Your next layer, I would call the Lower Level layer. That's the layer that sits on top of the primitive API and does maybe some raw things—something a little higher level than the primitive layer. Again, we're going to write unit tests for this layer, and this layer should be testable in its own right. We should be able to test everything coming in and everything going out. A lot of the time, this layer and these layers are probably unexported, but sometimes it's nice to export the Lower Level API because it's very usable if you write it correctly and gives developers or users more control over the things they need.

Then you're going to have your High Level API. This is where you're trying to do as much as you can for the user to make their life better. It sits on top of the Lower Level layer, which sits on top of the Primitive layer. Since every one of these levels has been coded and unit tested, by the time you get up here, we're really just thinking about ease of use for the developer, and we should be able to unit test this as well. Sometimes these higher-level functions require more integration-style tests, but we want to think about how we test every layer all the way up.

Eventually, your higher-level tests might be able to replace some of your lower-level and primitive unit tests because they cover those test cases for you. So there are times when I'm even writing unit tests that I know won't exist forever, because as I move up these levels of API, I'm getting the same code coverage on the tests I've written at lower levels—and that's okay. I cannot stress enough: I don't want you to be worried about throwing code away. Refactoring is about making something work and then looking at how we make it better, how we make it more readable, how we improve it. The best days are when we actually remove code. So don't get hung up on the idea that I might be writing some unit tests here that we'll eventually delete because we'll get better coverage higher up. Writing those tests right away ensures these layers are working.

This stuff is very, very important, and we want to keep these principles in mind as we move forward. As we start writing more code, you're going to see me focus on: What are the real problems in front of us? How do we layer this API—not only so it's usable, but so it's testable? And that doesn't mean we need interfaces. It means we need strong inputs and outputs around the data that we can test against. Then, once we have a concrete solution, how do we refactor the code to deal with change?