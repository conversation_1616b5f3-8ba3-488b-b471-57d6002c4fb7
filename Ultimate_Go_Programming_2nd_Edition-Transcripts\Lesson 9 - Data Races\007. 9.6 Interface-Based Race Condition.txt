So let's bring everything together with this really nasty program. It's somewhat more practical in illustrating how data races actually end up in production, especially if you're not using the race detector. Historically, without a tool as powerful as <PERSON>'s race detector, data races could easily make their way into production-level code.

Imagine this scenario: <PERSON> and <PERSON> are arguing with each other, locked in a dispute over who is more popular. <PERSON> believes he's the most popular; <PERSON> believes he is. To settle the debate, they hire a marketing company to track likes, launching a campaign to measure public support and determine who is truly more popular.

Let's look at some code we might write to solve this problem. On line 16, we define a `speaker` interface that specifies an active behavior: `speak`. The idea is that users will "speak up" when they like <PERSON> or when they like <PERSON>. Normally, we'd simply increment a like counter, but here we want to be able to detect harmful data races. So we'll do something a bit different—imagine that every time `speak` is called, it represents someone publicly showing support for either <PERSON> or <PERSON>.

The `Ben` data structure has a name field set to "<PERSON>". In the `speak` method, instead of just incrementing a like counter, we check whether <PERSON>'s name is still "<PERSON>". If <PERSON> is <PERSON>, everything is fine. But if a data race occurs, we might end up in a situation where <PERSON>'s name is unexpectedly "<PERSON>". When that happens, we'll log the inconsistency and return an error to shut down the program. This allows us to catch the corruption.

Here's <PERSON>, and here's <PERSON><PERSON> <PERSON>'s structure is identical to <PERSON>'s—just a name field—and the `speak` method is implemented the same way, except <PERSON> expects to always be "<PERSON>". So both types have a `speak` method that, in a normal scenario, would increment a like counter. But here, each one validates its own identity: <PERSON> must remain <PERSON>, Jerry must remain Jerry.

We start the program by creating a `<PERSON>` value—there it is—and a `Jerry` value. These are the concrete types that will hold our like counts. Note that we've implemented the `speak` methods on these concrete data types.

At the start, we use a `person` interface variable, and as you can see, we store Ben in it using pointer semantics. When we call `speak` on the interface, it will dynamically dispatch to either Ben's or Jerry's `speak` method, depending on which value is currently stored. In this case, it's Ben. So calling `speak` would increment Ben's like count to one.

Now imagine this is a real web service handling tens of thousands of incoming requests from supporters of Ben and Jerry. We have a goroutine simulating one or more incoming requests. This goroutine loads Ben into the interface and then calls `speak`. Line 65 is our write operation—a two-word write—because storing a value in an interface involves writing both the type information and the data pointer. Then line 66 performs the read by calling `speak`.

We have another goroutine simulating a different set of requests, where someone declares, "No, Jerry is the one I support!" This triggers a two-word write to store Jerry in the interface, followed by a call to `speak`.

Now we have a data race. I want to show you how quickly this leads to data corruption. Let's build and run it.

It doesn't take long before the program outputs: "Jerry says, 'Hello, my name is Ben.'" This is an integrity issue. Crucially, the code didn't crash. The worst possible outcome in a data race is when the program continues running without any obvious failure.

Let me explain why it doesn't crash. The output "Jerry says" means the first word of the interface—the type information—indicates that Jerry's method is being called. But only half of the two-word write completed before the `speak` call. The type word was updated to Jerry, but the data pointer still points to the Ben instance. It takes two operations to fully update an interface: changing the type and changing the pointer to the concrete data. Here, only the type was updated; the pointer wasn't. So when `speak` is called, it invokes Jerry's method, but on Ben's data.

As a result, Ben gets a like attributed to Jerry. We wouldn't notice this in normal execution because the program doesn't crash. The reason it doesn't crash is that both data structures—Ben and Jerry—have identical memory layouts. They are structurally the same: a single string field. So incrementing Ben's counter is memory-layout equivalent to incrementing Jerry's. In this case, even the names are the same length, so the memory access patterns are identical.

Because the data models are identical across the concrete types, the program runs without panicking. There's no segmentation fault, no memory violation—just silent data corruption.

You wouldn't discover this until someone in marketing says, "I like Ben as much as the next person, but there's no way he's that much more popular than Jerry—it just doesn't make statistical sense." Now you're told there's a bug in your program, and you need to find it. But there's no stack trace, no crash, no indication of where the corruption occurred.

This is why data races are so nasty.

Now, if I build this program with the race detector enabled, look what happens. When using the race detector, you build the binary with the `-race` flag and then run it. You can see how quickly it identifies the race: goroutine 7 and goroutine 6 are accessing the same memory location without synchronization. Specifically, there are two unsynchronized writes—one on line 76 and one on line 65. In this execution, those two writes occurred simultaneously. Great job, race detector.

You might say, "Bill, forget the race detector—I would've found this bug anyway." I'm going to tell you: no, you wouldn't have.

What if I set `GOMAXPROCS=1`, forcing the program to run on a single core? Now it's effectively single-threaded. Maybe I'm running this in Docker on my local machine, and I've restricted the container to one CPU core to conserve resources. I run the race detector again—and interestingly, it still detects the race. That's because the race detector doesn't rely on actual parallelism; it tracks memory accesses and synchronization events regardless of threading.

But now, let's rebuild the binary without the race detector and run it again. Look what happens: the race doesn't manifest. Technically, the race condition is still present in the code, but because we're running on a single thread, all operations appear to happen sequentially. The reads and writes are happening in a way that appears atomic—not because they are, but by accident of execution order.

In this single-threaded environment, our tests pass. The code appears to work perfectly. But when this code moves to production—where we're likely running on multiple cores and true parallelism exists—the race condition starts causing real data corruption.

Without the race detector, we'd have no way to catch this during development. This is a classic, historical pattern of how data races have entered production systems. And that's why they're so dangerous: they can go undetected for years. There's no crash, no stack trace, no memory error—just incorrect data. And detecting data corruption by code inspection alone is extremely difficult.