Let me show you a more practical example using the standard library's HTTP and NET packages to demonstrate how we can implement cancellations using the concepts I just showed you, but in a much more practical way. This is something I use in production code all the time.

Here we are in main. You'll see I'm importing the http, time, os, and context packages. The idea is this: let's make an HTTP GET request to an external service—like my web server that hosts my blog—but let's not wait forever for the response. Instead, we'll give it a limited amount of time to complete. If it succeeds within that time, great. If not, we cancel the request and move on.

So here's what happens. This is our main goroutine—our main thread of execution. First, I create the HTTP request. We're doing a GET call to fetch the RSS feed of my blog, no post data, and we get back the request variable with no error.

Then, on line 28, I create a context with a timeout of 50 milliseconds using `context.WithTimeout`. This context will enforce a 50-millisecond deadline on the entire operation. Immediately after, I defer the cancel function. Notice that the parent context comes from the request itself—the HTTP request already has a context, and we want to use it instead of starting from `context.Background`. By doing this, we inherit everything from the request's context and layer on the 50-millisecond timeout. That's powerful.

Next, I create a transport. The transport represents a pool of socket connections to a resource—in this case, the server hosting my blog. Think of it as a connection pool sitting behind our beautiful cloud infrastructure. This transport will manage persistent socket connections, and it's also where we configure various timeouts: dial timeouts, keep-alive settings, idle timeouts, handshake timeouts, and read/write timeouts. I'm showing the default transport values here, but in practice, you should measure and tune these based on your needs.

A transport allows us to maintain efficient, reusable connections—ideally, one transport per unique service—to achieve better performance through connection reuse. In this case, the transport can maintain up to 100 idle connections.

Then we create an HTTP client. The client wraps the transport and provides a clean API for making requests. It uses the transport under the hood to communicate with the remote service.

Now here's the critical part: if I let the main goroutine perform this HTTP request directly, I won't be able to cancel it. So instead, I launch a new goroutine to do the work. But before that, I create a buffered channel of size one—this will be used for signaling when the work is done.

Here comes our "employee" goroutine—let's call it E. This goroutine has access to the client via closure. What does it do? It calls `client.Do` on the request. That call triggers a series of actions: DNS lookup, establishing a socket connection, sending the request, and waiting for a response. Only when the connection is established and data starts streaming will `client.Do` return.

Meanwhile, back in the main goroutine, we're blocked, waiting to receive on the channel. This is the "wait for result" pattern. If `client.Do` fails, the employee goroutine sends the error back through the channel. If it succeeds, we proceed.

We always defer closing the response body—otherwise, we'd leak memory. Once the body is closed, we use `io.Copy` to stream the response data—specifically, the XML of the RSS feed—directly to standard output so we can see it.

When all the work is complete—whether successful or not—the employee goroutine signals completion by sending an error value (which may be nil) on the channel. Since the channel is buffered with capacity one, the send will not block, even if the receiver isn't ready yet. This means the send can happen independently of the receive.

Back in the main goroutine, we use a `select` statement to wait on two possible outcomes. First, we wait to receive from the channel—meaning the employee has finished its work, either successfully or with an error. Second, we wait on the context's `Done` channel. By using `select`, we're effectively starting a 50-millisecond timer.

If the context times out—meaning the `Done` case fires before the employee finishes—then we know the operation took too long. And here's the beautiful part: because we have access to the transport, and the transport supports cancellation, calling `cancel()` will interrupt any ongoing operations in `client.Do`. This causes the employee goroutine to fail immediately, shutting down cleanly.

I include a receive on line 75 to confirm that the goroutine has actually shut down—this isn't strictly necessary, but it ensures we don't proceed until the worker is done. Ideally, we want the request to succeed, but if it doesn't, we can cancel it early and avoid wasting CPU cycles.

Let's run the program. Right now, the timeout is set to 50 milliseconds—barely enough time to even start a DNS lookup. I build and run it. Immediately, we see "timeout, cancel work" and the error: "request canceled while waiting for connection."

Here's what happened: we launched the employee goroutine, which entered `client.Do`. That call began trying to resolve the DNS and establish a socket connection. But before it could complete, the 50-millisecond timeout fired. The context was canceled, which propagated through the transport, causing `client.Do` to fail immediately. The employee goroutine reported the error, shut down, and we moved on.

Now, let's change the timeout from 50 milliseconds to five seconds—plenty of time. I rebuild and run again. This time, we see the full RSS feed output from goandgo.net.

What changed? This time, the `client.Do` call completed within the five-second window. We successfully established the connection, read the response, streamed it to standard output, and the employee signaled completion before the timeout. The `select` statement received the success signal, and the program exited cleanly.

The first run showed cancellation in action: the request was terminated early because it took too long. The second run showed successful completion within the allowed time.

What's even more powerful is that this pattern allows not only the main goroutine (the manager) to walk away, but also to terminate the employee goroutine early. That means we're not wasting CPU cycles on work we no longer need. This is a perfect example of how Go's context and cancellation mechanisms can be used effectively in network programming—allowing us to gracefully cancel in-progress operations when they're no longer relevant, improving efficiency and responsiveness.