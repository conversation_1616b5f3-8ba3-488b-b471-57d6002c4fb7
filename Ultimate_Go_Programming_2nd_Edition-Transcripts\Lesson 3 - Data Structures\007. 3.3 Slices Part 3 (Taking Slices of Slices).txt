All right, now let's really learn why slices are called slices and the efficiencies we're going to get from using pointers. This is really important stuff. Again, slices are your most important data structure when you're coding in Go. So let's start right here. We're going to make a slice of strings again, starting with a length of five and a capacity of eight. This backing array is going to have that capacity of eight. We're going to point to it and we're going to have apple, orange, banana, grape, and plum—apple, orange, banana, grape, and plum. You can see here we've got the three extra elements of capacity. Remember, length is what we are allowed to access—that's these five elements here. Capacity is there for future growth. I'm using my inspect slice function that we looked at before.

Now I'm going to add some extra print lines so I can look at the output of this very first slice. You can see here: length five, capacity eight. We have a contiguous block of memory underneath—apple, orange, banana, grape, plum, 08 08 08 08—all eight together. Okay, great, so we've got that in place.

Now one of the important things about a slice is the efficiencies that come with it as it relates to the data we're working with. What you want to see here on line 24 is our slicing syntax. What you can do is take any existing slice value, like we have here, and create a new slice value sharing the backing array for efficiency. The idea again is that the only thing that has to be on the heap is the backing array. If anything has to escape, the only thing that should ever escape is the backing array—the thing being shared. Our slice values get to stay on the stack.

So what this syntax is saying is: take your slice and say a:b, and what that a:b means is index a through index b, a through b. Now the first value is very intuitive—two, index two—and what that is saying is: let's create a new slice value where the pointer is going to point at index two of slice one. Okay, we get that—that's no problem. That should mean now that we're going to be basically starting here as it relates to this new slice, which also now means that that's going to be index zero of this second slice. But the b value is a little bit more complex. I wish it wasn't, but it is. What this syntax really means is: go from index a through b, but not including b. In other words, indexes two through four, not including four.

When we say not including four, then what we're saying is that we want a length of two, because our length is only going to be able to access indexes two through four, not including four. But our capacity is always going to be the total number of elements from that pointer position—one, two, three, four, five, six. So here we are: we have six elements on the capacity side, two elements on the length.

Now I hate this idea of two through four, not including four—it's too complex. What I like to tell people is, if you don't know your length, you shouldn't be slicing. Your length is everything when it comes to slicing. So what I like to do is think of the slice like this: let's start at index a, or two, but then let's do a plus whatever length you want. If you always slice with the idea of length, guess what—you'll never, ever make a mistake. And that's what we did here. We said: start at index two, I want a length of two, so two plus two is four. There we are. We've got our new slice.

Now what's very brilliant about this is, if I run this code, what we see is the original slice at a length of five and a capacity of eight. There it is, working through apple through plum. But our new slice value is only a length of two, capacity of six. Notice that when we range over the slice, we're only ranging over its length. But also notice that banana and grape in both slices have the same exact address. Look—that really shows you that we are sharing this backing array and we're sharing it efficiently. This is the only cache line that has to be cached by the core that's working with these two different slices. These slice values are like a view—a unique view of the core data structure, the larger structure here that we're going to be sharing.

But I told you that pointers are dangerous, sharing is dangerous, pointer semantics can be dangerous because of side effects. And I want to show you some of these side effects right now. Look at what I do on line 30. On line 30, I take slice two and index zero. Notice something: banana can be accessed in two different ways—index two on slice one, or index zero on slice two. This is very, very dangerous. I've got two different ways to come in and do this mutation. I use slice two, index zero, and I come in and I change this out. Here's that concept of a side effect. Let me put those print statements back in here. Now, after we make this mutation on line 30, let me display the value of both slices. What you're going to see is that both slices see the change. We see it in index two on slice one. We see it in index zero on slice two. This is really the beginning of a nasty side effect. What if we didn't want slice one to see this change? What if we wanted to isolate this change just to slice two? At this moment, we can't—because we're sharing the backing array in the name of efficiency.

All right, let's put this back for a second and let's try something else. Let's go ahead and instead of going right into that index position and making that change, why don't we try to use the append call? In other words, I'm going to come in here and say: let's use append and we'll append it to slice two. Now, what kind of behavior should we see when we use append? Maybe life will get better for us. I don't know—let's take a look.

So we go ahead here and we say: let's append the new value to slice two. Append gets a copy of the slice value and asks: are length and capacity the same? The answer is no—brilliant, we can add to the length because we have the capacity. But what's going to happen now is append is going to bring in this element right here. It's going to bring that element in, and when it brings this element in, it brings it in. This will now be index two. Well, what happens? Since we brought that index in, we now have to go ahead and replace the value that's there—and we will—and we'll replace it with "change". Look, we just replaced plum with "change". Who's going to see that change? Guess what? Everybody sees it again. We didn't really fix our problem. We didn't get rid of our side effect. All we did was move it two indexes down, because the append brought in an element that was already in use by slice one. This is really scary, nasty stuff.

But let me ask you a question for a second. Let's put this back the way it was. Let's pull this out—we'll pull all that back out. We're going to make that plum again. And let me ask you a question: the whole problem with append was that our length and capacity were not the same. But what if length and capacity could be the same when we make the slice? What if this could be two and two? I mean, if during the slicing operation, we could add a third value and say: let's not just adjust the length of this new slice, let's also adjust the capacity to also be two—a through b, not including b, and a through c, not including c. If we could adjust the length and the capacity to be the same, what would append do?

Well, append would say: do I have any extra capacity? And the answer would be no. And append would basically give us a copy-on-write. Look at what happens when I run this code. Notice now that we don't have a length of two and a capacity of six—we have a capacity of two. We're still sharing the same backing array, but now after the append, look at the addresses for banana and grape—they're different. On this call to append, we've made a copy of just the elements that we were working with—and we're good. Copy-on-write—we just got rid of the side effects because of the three-index slice.

Three-index slices are fantastic to help reduce side effects when you know you're going to be doing appends on a second slice value and you don't want those mutations to affect the original or other slice values that are sharing the backing array. Very cool stuff.

But there also may be times where you have to manually do your own copies. Try to avoid these, but sometimes you have to do it—and that's why Go gives you the built-in function copy. The built-in function copy will let you take the source of one slice and make it the destination of the other. You can see here that I'm making a slice of the same length as our slice one. I'm using slice one as a source. Slice three is the destination. And you'll see that slice one and slice three have different memory addresses when we do this—because they're really, at the end of the day, this is going to have its own memory address. Slice three, because we've manually done the copy as opposed to using the three-index slice.

So you're starting to see how pointers give us levels of efficiency. Sharing means that this data's not duplicated, which means the hardware only has to worry about copying this one thing. We get to reuse it, we get to create different views. But you can also see that now that we're sharing data, we have different ways of getting to it and therefore different ways to mutate it. And now we've got to become careful about when we're mutating and when we're not—especially when we're mutating behind someone's back to the point where they didn't expect it, and now they're going to have problems with the data.