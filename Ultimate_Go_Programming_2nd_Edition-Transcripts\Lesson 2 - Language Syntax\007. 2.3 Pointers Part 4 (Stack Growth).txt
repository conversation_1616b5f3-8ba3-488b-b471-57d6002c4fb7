We've just started learning how escape analysis teaches us when something allocates or not. Whether something is shared determines whether it allocates. There's another aspect of allocation in Go: if the compiler doesn't know the size of a value at compile time, it must immediately construct it on the heap. The function frames I've been drawing are sized at compile time. These frames are not dynamic, so if the compiler doesn't know the size of a value ahead of time, it cannot place it on the stack.

The compiler knows the size of many things at compile time—struct types, built-in types, and other types we'll be using. But sometimes you have collections whose size depends on a variable, which gives the compiler no idea what the final size will be. In those cases, automatic allocation occurs. This is another place where allocation happens in Go: when the compiler doesn't know the size of a value, it triggers an automatic allocation.

We've discussed how Go's stacks are very small. The operating system stack is about 1MB, but a Go stack starts at just 2KB. What happens when a goroutine makes many function calls and eventually runs out of stack space? Can we just terminate the goroutine like we do with a thread? No, we won't. Remember, Go prioritizes integrity first and minimizing resource usage second. We always aim to reduce and minimize the resources we need. In today's world of cloud computing, shared resources are critical. That 2KB stack is a major part of that efficiency.

But when we run out of stack space, we need a new stack. This mechanism is unique to <PERSON>—I'd never seen anything like it before. Imagine we have a stack with some values, and we're even sharing those values as we go deeper into the call stack. Eventually, we run out of space. Go handles this with what are called contiguous stacks. It allocates a new stack that's 25% larger than the original, then copies all the existing frames over. The pointers involved are relative, so adjusting them is fast. During the function call, the goroutine takes a small latency hit: creating the larger stack, copying the frames, and fixing up the pointers.

We accept this cost in Go for two reasons: integrity and resource minimization. I've said it before—nothing is free. Integrity and minimizing resource usage come at a price. But this doesn't happen frequently. 2KB is usually more than enough because most call stacks don't go deeper than 10 levels. Plus, the compiler has optimizations to keep frames very small. This trade-off is well-balanced and effective. Still, stack growth can happen.

What this means is that values on your stack can potentially move in memory. This is a whole new concept. Let me show you an example with a small Go program. I have a constant of size 10, and here's the main function. I'm constructing a string value—"HELLO"—and I'm sharing that string down the call stack, as you see on line 14. I've also allocated an array. We know the size of an array at compile time—it's part of the type. On the playground, an integer is 4 bytes, so 10 integers make a 40-byte array. This array will be placed on the stack frame, and the frame remains small.

The function `stackCopy` is recursive—it calls itself repeatedly, always sharing the string down the call stack. If I run this for 10 iterations, you'll see that the address of the string inside the main frame doesn't change. Perfect. Our 2KB stack is sufficient for this program.

But what if I change the 40-byte array to a 4,000-byte array? Remember, we started with only a 2KB stack. That means on the first call to `stackCopy`, we'll immediately trigger stack growth. And since the new stack is only 25% larger, that growth may not be enough, so we'll see additional expansions as the program runs. Watch what happens now.

You'll notice that the string's address has already changed—this is on a new stack. Look at index two: another new address, 55fa0. That's another stack expansion. And at index six, it happens again. Why? Because the 4KB array is being kept on the stack frame—it doesn't need to escape. Recall that escape analysis favors the stack; heap allocation is the exception, used only when integrity requires it. This is a classic case where nothing escapes.

But you can clearly see the stack has grown multiple times. This is powerful: we can start with very small stacks, enabling us to run hundreds of thousands of goroutines, and the stack grows only when needed.

Now consider the side effect: since stack values can move in memory, this creates an important constraint in Go. No stack can have a pointer to another stack. Imagine having hundreds of thousands of goroutines with stacks pointing to each other. That would be chaos if one stack needed to grow. Think about it: if one stack grows, we'd have to track every pointer referencing it and update them all. That would cause massive stop-the-world latency—completely unmanageable.

So one of Go's key design decisions is this: because stacks can move, the only pointers to stack memory must be local. Stack memory belongs exclusively to its goroutine. It cannot be shared across goroutines. This is exactly where escape analysis comes into play—values that need to be shared across goroutine boundaries must move to the heap.

The heap is used for three main reasons: values shared across goroutines, values that must escape due to lifetime or integrity concerns, and values whose size isn't known at compile time. These are the three fundamental constraints governing allocation in Go.

All of this exists because we want to support hundreds of thousands of goroutines, which means we need hundreds of thousands of stacks. We couldn't do that if each stack were megabytes in size. They must be as small as possible. We accept a small runtime cost—stack copying—for the sake of integrity and minimal memory usage, because those are higher priorities in Go. What we care about isn't making our code the absolute fastest—it's making it fast enough.