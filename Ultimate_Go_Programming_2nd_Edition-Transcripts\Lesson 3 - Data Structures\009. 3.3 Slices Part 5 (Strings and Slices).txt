I want to show you more practical slicing code and at the same time teach you a little bit more about what strings are in Go. Let's take a look at this sample program.

Strings in Go are UTF-8 based. In fact, the entire language is UTF-8 based. If you're not saving your source code as UTF-8, you're going to have real problems with the literal strings and raw strings stored in your code files. Everything must be UTF-8 encoded, all the way through.

What's interesting about UTF-8 is that it's a three-layer character set. At the very bottom, you have bytes—and really, we always consider strings to be sequences of bytes at the end of the day. In the middle, you have what are called code points. A code point is a 32-bit or 4-byte value. Then, above code points, you have characters. The idea is that a code point can be anywhere from one to four bytes, and a character can consist of one or more code points. This creates a kind of n-tiered character set.

Let's look at some code. On line 38, I have a literal string that is 18 bytes long. Why 18 bytes? If we examine the string, we see that the first Chinese character requires three bytes of UTF-8 encoding to represent one code point. The next Chinese character also requires three bytes for its code point. After that, all remaining characters are single-byte ASCII characters. So we have three bytes, three bytes, and then twelve single-byte characters—totaling 18 bytes.

Now look at the next variable. We're creating a variable named `buf`. Note that `buf` is an array, not a slice. It's an array of four bytes, which corresponds to the maximum number of bytes needed to represent a single code point in UTF-8. This is a constant in Go. The array is initialized to its zero value using the `var` keyword.

Things get interesting here: I'm about to range over the string. You might wonder, "Can I really range over a string?" Yes, you can. And when we do, we're using value semantics. But the key question is: what exactly do we get during iteration? We get two values—the index position and a copy of something. What is that something?

When we range over a string, we get the index position of each code point and a copy of that code point. We have three possibilities: we could get every byte, every code point, or every character. Many people assume we iterate by character, but that's not correct. We iterate by code point. The index position reflects the byte offset of each code point in the string.

This means that on the first iteration, we start at index 0. On the second iteration, the next code point begins at byte index 3, because the first Chinese character's code point occupies three bytes. The third iteration starts at byte index 6, and then we proceed one byte at a time through the ASCII characters.

So when we range over a string, we're iterating code point by code point. The variable `r` represents the code point we just iterated over. In Go, `r` is of type `rune`. `rune` is not a distinct type—it's an alias for `int32`. Similarly, `byte` is an alias for `uint8`. So `r` is a 32-bit, 4-byte value representing a Unicode code point.

Let's see how the code works. On the first iteration, `i` equals 0—the starting index. We call `utf8.RuneLen` to determine how many bytes this code point requires. For the first Chinese character, that's 3. Then we calculate `si`, the sum of `i` and `rl`, which gives us 3. Now we reach line 54, which uses the built-in `copy` function.

`copy` takes two arguments: destination and source. Notice that `copy` works with both slices and strings, but since strings are immutable, they can only be sources. Here, we're slicing the string `s[i:si]`—from index `i` (0) to `si` (3). This slicing operation doesn't create a slice; it creates a new string value of length 3, pointing to those three bytes.

Our source is this 3-byte substring. Our destination is the buffer `buf`. But there's a problem: `copy` works with slices and strings, not arrays. `buf` is an array. However, Go's syntax allows us to apply slicing syntax to arrays. One of my favorite sayings in Go is: "Every array is just a slice waiting to happen." 

By writing `buf[:]`, we create a new slice value that uses the array as its backing store, with length and capacity both set to 4. This is brilliant. Now we can use the local array in the `copy` function. `copy` will copy the first three bytes of the source string into the first three positions of the buffer.

Then, before printing, we slice the buffer again: `buf[:rl]`, where `rl` is 3. This ensures we only display the three bytes that were actually copied, not the full four-byte buffer.

On the next iteration, `i` is now 3, `rl` is still 3, and `si` becomes 6. We create a new substring from `s[3:6]`, which captures the second Chinese character. We copy those three bytes into the buffer and display them. After that, we process the single-byte ASCII characters one by one.

Looking at the output, you can see we're ranging over the string code point by code point. You see the three bytes for the first Chinese character, the next three for the second, and I'm even able to display the characters themselves correctly. Then we move into the single-byte characters, showing their byte values.

This is practical code that demonstrates how strings work in Go and gives you a deeper understanding of UTF-8, code points, and the relationship between arrays, slices, and strings.