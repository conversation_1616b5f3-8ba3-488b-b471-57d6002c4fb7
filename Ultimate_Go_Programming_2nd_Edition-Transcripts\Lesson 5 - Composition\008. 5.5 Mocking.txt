So I want to reiterate that I don't want to be using interfaces anytime we have to think about mocking. But I really want to show you some mechanics in Go regarding convention over configuration, because it's helped us write less code and has taken the burden of testing—specifically, worrying about users' tests—off the API developer.

Let's start with this piece of code here. Imagine one day I have a client. My client loves the PubSub type of strategy: we have a message bus in the middle, and all of our services connect to it. Services talk to each other, and we get service discovery through the message bus. It's a really nice starting pattern if you're trying to do microservices.

What I've done here is respond to a request from my client. They said, "Bill, we have our own internal message bus. We've had it for years. We use it, we love it. We want to start writing code in Go, but we don't have a package or an API that lets Go developers connect to PubSub and all that good stuff for the message bus. Will you write the package?" I said, "Absolutely. I'll implement the binary protocols, I'll do everything you need." But my question to the client was, "How many message systems do I have to worry about?" They said, "No, Bill, just one—just this one. This is the only one that matters." I asked, "There's not a second one?" They replied, "No, no, no, <PERSON>, just this."

At that point, I realized I don't need interfaces. There's nothing to decouple. There's only one implementation, and I don't need anything special for my users. So I designed an API entirely in the concrete. I came up with this type, PubSub—it has other fields like host and connectivity information. I have a factory function that returns a pointer to the concrete type, and then we implement our API. Now, we can pretend this is a 10-method API. It's a method-based API because PubSub has state, connectivity issues, and so on. We've implemented methods like publish and subscribe—let's assume that's all done brilliantly.

Now it's time to write tests. I'm not going to add an interface just to write tests, because I need to make sure my binary protocol works. So instead, I use Docker. I put the message bus system that this client uses into a Docker container. Every time I run `go test`, I bring up the message bus and verify that my entire API and all the binary protocols work as expected. Nothing is mocked. When this code is ready for a user, I know everything absolutely works.

I finish the code and go over to someone on another team and say, "Here's the PubSub package—start using it." A couple of days later, someone—let's say Jack—comes over to me and says, "Bill, Bill, come here, I've got a problem." I ask, "Dude, did you find a bug in PubSub?" He says, "Oh no, man, I haven't even gotten that far. I started realizing I need to write tests against PubSub, but I won't have a PubSub system available when I run my tests. I need to mock the PubSub system."

I think about it and realize he's right. For his application and his application's tests, he needs to mock. He should be able to assume the PubSub API works. He should be able to mock the behavior: if I subscribe to this and publish that, I get everything back. I tell him, "You're right—you need to mock your tests." He says, "Great, Bill, I appreciate that you understand what I need to do. But you didn't give me an interface, so I can't mock it. Can you go back and add an interface to your package?"

I say, "Dude, no, no, no, no, no—I'm not adding an interface to my package, because I don't need it. I don't have a need to mock. My tests hit the real system thanks to Docker. My API has one and only one implementation. You need to mock, not me—so I'm not going to provide you an interface."

He says, "Bill, why are you being a jerk? You know I need the interface—just give it to me." And I respond, "I'm not being a jerk. What you're not understanding is this is Go. Go says that I only have to worry about my tests—I don't have to worry about your tests. Your code, your tests need this interface, not mine. So sit down, Jack, and let me show you something."

What I tell Jack clearly is this: there is nothing—nothing—stopping you from defining your own interface. Remember, Go is about convention over configuration. Isn't it true that if Jack implements an interface containing only the methods he's using—because he might not be using all of them—then any concrete type I've defined will also satisfy that interface?

Yes. Think about how convention over configuration helps us reduce code and cognitive load. Jack can go ahead and define the interface he needs and write his entire application using that interface. I don't have to provide it. And yet, any concrete value or pointer that comes out of my package can satisfy this interface for Jack.

So Jack, in his tests, defines a concrete type called mock. He implements the API methods he needs, and now he's able to mock. When his application runs, he either calls `pubsub.New` and gets the concrete type from my package, or when his tests run, he creates his own mock concrete type. But his entire application is built not on the concrete type, but on this value-less interface.

Jack doesn't need me. My code stays clean, streamlined. The person who needs decoupling—Jack—defines the interface. This is what's beautiful about Go and the idea of convention over configuration: we've shifted the responsibility. If you need to worry about tests, you worry about your own tests. I don't have to do anything special in my code just because you have to test. This is brilliant—brilliant stuff coming out of Go.