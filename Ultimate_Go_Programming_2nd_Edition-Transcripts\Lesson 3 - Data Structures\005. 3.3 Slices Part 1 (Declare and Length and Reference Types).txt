So we just finished talking about arrays, and I showed you a lot of mechanical sympathies around them and started seeing more of the value in the pointer semantics around that. But slices are the most important data structure in Go. This is something that you must learn, you must master—you can't cheat on this—because all of the data you'll be working with, or at least the majority of it, should be and probably will be stored in slices. This is your go-to data structure.

Are there times when a slice is not reasonable or practical to use? Absolutely. But right now, until you know it's not reasonable or practical, this is the direction you should take. And again, prefer a slice of values over a slice of pointers when that is reasonable and practical.

Let's start with some slice code right here. Notice that I'm using the `make` function. This is a built-in function in Go that allows us to create three of the reference types that are also built into the language. Up until now, we've been using built-in types and user-defined or struct types. Go has another class of types called reference types: these are slices, maps, channels, interface values, and functions. They're all reference types.

They're called reference types because they are data structures that contain a pointer. They're also reference types because when any of these types are set to their zero value—such as when we create a variable of a slice or a map and don't initialize it—they are considered to be `nil` in this language. It's like a pointer set to its zero value being `nil`.

A string is actually very close to being a reference type. The problem is that when a string is set to its zero value, it's not `nil`—it's empty. So I can't really put it in that class. But we're using the built-in function `make` to create this slice, and we'll use `make` when we already know ahead of time how much memory to allocate for its backing data structure, which is an array.

Once this `make` call is done on line 13, what we really have is this `fruits` slice. A slice is a three-word or 24-byte data structure on AMD64 architectures, and it's very similar to a string, where you have a pointer and a length. In this case, the length is five, which means we're going to have five strings. So what we end up with is an array of five elements—zero, one, two, three, four—with our pointer pointing to that, kind of similar to a string. But we also have a third value—that's why it's three words. This is capacity.

When only the length is specified in the `make` call, the capacity matches the length. So here's the question: what is the difference between length and capacity? Length represents the total number of elements you can access from this pointer position. And I want you to know that the `make` call is going to set all of the elements in the backing array to their zero value state.

So length is the total number of elements that you can access from that pointer position. Capacity is the total number of elements, period, that exist in the backing array from that pointer position. Capacity can be larger than length, but not the other way around.

In this particular case, I've initialized the slice with "apple", "orange", "banana", "grape", and "plum". Then, the same cost applies as we saw before—we're only going to have to copy the two-word string value.

Now, if you try to access a slice beyond its length, like you see on line 21, you're going to get a runtime error. It's like going past the bounds of an array. Your length sets the bounds, just like an array has a bound set at compile time.

This is also really important: I want you to notice that we're passing a value of this slice into the print call. In other words, the slice value—just like the string—is designed to use value semantics. As we're going to learn, our built-in reference types are designed around value semantics. They're designed to be kept on the stack.

Notice again I've got this slice value on the current stack frame. Below it, on the frame for `print`, we've got this string value, and thanks to the pointer, they share the backing array. There's efficiency in sharing, and we also have consistency in copy cost. This array could contain millions of strings, but as we pass it around across program boundaries, the cost is always consistent: 24 bytes. How beautiful is that?

The only thing that might end up on the heap—if anything—is the thing being shared: the backing array. This is beautiful—this is balance, minimizing allocations, and only allocating those things that need to be allocated, which are the things that are going to be shared.

Again, right now there's no reason for this backing array to be allocated on the heap because we know its size at compile time. We've given the compiler a very specific number here: five. We're never leaving the stack frame. So even though there's an allocation here, it should be on the stack frame. But at the end of the day, if we end up making a copy of the slice value and passing it up the call stack, that's the only thing that would have to be allocated.

Why? Now let's take a look at a slice where the capacity is larger than the length. Here I am again using the `make` call, but instead of just saying five, I've said five, eight. What I've now said is: I want a slice whose length is five, but whose capacity is eight. That means I'm going to have three more elements here—three extra slots: indices five, six, and seven—where we have additional capacity.

Capacity is for growth. I want you to remember that. Length is what we have access to today; capacity is for growth. It means we have the ability to grow this slice beyond the length of five efficiently, up to eight elements. Capacity is there for growth—but efficient growth.

Now there's a function here that I wrote called `inspectSlice`. We're going to use this in some of the other examples we'll go through. The first thing I want you to look at is the parameter. See how we're declaring the slice value? That's what we want. There's no asterisk here. If I put an asterisk in here, we'd have an array. If I write `[4]string`, I'm asking for an array of four strings. But here, I'm asking for the 24-byte slice value. That's what I'm asking for.

Again, as we're going to continue to talk about: I don't want you sharing the slice value. It is designed to stay on your stack, it's designed to use value semantics, and we should be making copies of it. So when we call `inspectSlice`, it will get its own copy of the slice value—the 24 bytes. There it is, and `inspectSlice` will operate against its own copy.

There are two other built-in functions that you see us using here. One is `len`, which is used for maps, slices, and channels. The other is `cap`, which is specific to slices—only slices have a capacity. These will give us the values five and eight when I run this code.

If I run it right now, we should see output showing length five, capacity eight, and we should see a contiguous block of memory. As I range over the slice using value semantics but asking for the address of every element, you can see that this memory is contiguous. Then we have an eight-byte stride—zero, eight, sixteen, twenty-four, thirty-two—for every string. All of that is predictable because we know that a string is an eight-byte value on the playground.

Our `inspectSlice` function we're going to use very soon to do more, because one of the problems with slices is this pointer. This pointer means that as we begin doing things like mutation, we could create side effects—and it's something we have to be aware of when working with this data structure.