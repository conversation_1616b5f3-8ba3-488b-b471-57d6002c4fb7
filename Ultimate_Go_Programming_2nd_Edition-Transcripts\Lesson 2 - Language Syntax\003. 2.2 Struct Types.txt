Okay, next we need to learn about struct types—or really, user-defined types. The language would be useless if you couldn't define your own data types, right? Go isn't being novel here; lots of languages allow this. There are some important things I want to share with you—not necessarily teaching you what a user-defined type is, but how they exist in Go.

There's a great quote by <PERSON>, who coined the phrase "mechanical sympathy." Look him up if you don't know him. He said: "Implicit conversion of types is a Halloween special of coding. Whoever thought of them deserves their own special hell." We'll come back to this idea of implicit conversion. I really love that quote.

Let's do a little code review and start with the basics around user-defined types. Here it is in Go: on line 10, we're defining a named type called `example`. It's a struct type—a composite type—made up of three existing types: `bool`, `int16`, and `float32`. Nothing special going on there.

On line 20, I'm creating a value of type `example`, named `e1`, set to its zero value. You won't hear me use the word "object" in Go. We create values in Go. You'll continue to hear me say that. Listen to the language and the words I'm using. I'm going to be very consistent with them, and it will help us throughout the class. So again, on line 20, we are constructing or creating a value of type `example`, named `e1`, set to its zero value.

But let's go back and talk about the type declaration, because I want to focus—especially when performance matters—on the idea that we optimize for correctness first. At the same time, there are some interesting things about structs that I want to share. And if we're really going to understand cost, I told you we have to understand allocations. So the first question I'm going to ask you is: how much memory is allocated for the `e1` variable on line 20? Any time we construct a value of type `example`, how much memory has to be allocated?

Your first guess might be: "Well, Bill, it's a composite type." So you might do the following: "The first field is a flag—that's a boolean, which is one byte. Then I've got a counter, which is `int16`—that's two bytes." Draw a little line for the two bytes. "And then we've got pi, which is a 4-byte value—a `float32`." If you add these up, you get a total of seven bytes. So it seems like every time you construct a value of type `example`, it's going to be seven bytes.

That's not a bad guess—it's very realistic. But it's wrong. The reason it's wrong is because of alignments. Alignments come from the hardware and exist to make reading and writing memory as efficient as possible. As we move across the hardware in this class, we'll see there are different memory boundaries the hardware deals with. One of these is the hardware's ability to pull data in and out efficiently. This leads to machine word boundaries.

I want you to get a general sense of what's happening with alignments and why they exist—and why this isn't a 7-byte value, but actually an 8-byte value. If we think of memory in terms of these word boundaries, the hardware can read and write an entire word in one instruction. But if we allow a 2-byte value to be placed across a word boundary—say, starting at byte 1 and ending at byte 2—then it would take two operations to read it and two to write it. That's inefficient and sets us up for other problems. The hardware doesn't want that, and neither do we.

The idea behind alignments is to ensure that values don't cross these word boundaries when they can fit entirely within one. So for a 2-byte value, we make sure it always falls within a single word boundary. This leads to the concept of padding inside structs.

Here's how it works: a 1-byte value like a `bool` can fit anywhere—it will never cross a boundary because a byte is the basic unit of memory. But a 2-byte value like `int16` must be aligned so it doesn't straddle boundaries. We ensure this by making sure its address is a multiple of 2—so address 0, 2, 4, 6, 8, etc. A 4-byte value like `float32` must fall on a 4-byte alignment—addresses 0, 4, 8, etc. An 8-byte value like `int64` must fall on an 8-byte boundary—address 0, 8, 16, etc.

We determine a value's alignment based on its size and ensure it's placed correctly in memory.

Now let's apply this to our `example` struct. We originally said: flag (1 byte), counter (`int16`, 2 bytes), pi (`float32`, 4 bytes). That's 7 bytes. But it's actually 8.

Let’s map it by address. Suppose `flag` starts at address 0. That’s fine. Now `counter` is 2 bytes and must be aligned on a 2-byte boundary—so it must start at address 2. But `flag` only takes up address 0. What happens to address 1? That byte is skipped. It becomes padding. It's part of the struct's memory but unused.

So now we have: address 0 (flag), address 1 (padding), address 2–3 (counter), address 4–7 (pi). That’s 8 bytes total. The struct is 8 bytes, not 7.

Now, before we make this more complex, I want to emphasize: I always focus on optimizing for correctness. I don't care about padding unless it becomes a problem. The only way padding becomes a problem is if it causes excessive memory usage—increasing our memory footprint. But that’s a micro-optimization we’d only consider after profiling.

For example, what if instead of `int16`, we used `int32`? Now `counter` is 4 bytes and must start at address 4. That means we have 3 bytes of padding between `flag` and `counter`. If we used `int64`, it would need to start at address 8—meaning 7 bytes of padding after the 1-byte `flag`.

You can see how this adds up. Imagine a struct with multiple fields like this—suddenly you have 21 extra bytes of padding. Again, this is micro-optimization. If the field order makes the struct more readable or logically grouped, I’ll accept the padding until a memory profile shows it’s a problem.

If we truly want to minimize padding, we should order fields from largest to smallest. This reduces padding by aligning larger fields first, letting smaller ones fill in the gaps. But here’s the catch: a struct itself must also be aligned based on its largest field. So if the largest field is 8 bytes, the entire struct must be aligned on an 8-byte boundary. That means there could be padding at the end too.

If we removed `int64` and only had `int32`, the struct would need 4-byte alignment—so padding might be added at the end to make the total size a multiple of 4.

But here’s my point: if I see a struct ordered by field size from largest to smallest, it raises a red flag. I’ll ask: why are you pre-optimizing? Do you have a memory profile showing this is necessary? If not, I’ll ask you to reorder the fields based on correctness and readability—grouping related fields together—rather than jumping straight to performance optimization.

You might ask, "Why doesn’t the language do this automatically?" Because Go doesn’t go behind your back to change memory layout. You need to understand and control your memory layouts. You get to make them as precise as needed. It’s not the language’s job to pre-optimize for you. Some languages do, but Go doesn’t. That would obscure the impact of your decisions.

So understanding struct size means understanding alignments and padding. Based on field size—anywhere in memory—an `int16` must be 2-byte aligned, a `float32` 4-byte aligned, a `bool` can go anywhere. This can create padding.

In our case, the `example` struct is 8 bytes. So when we construct `e1` on line 20, it costs 8 bytes of memory.

Now look at line 27. It shows how we construct a value when we don’t want the zero value. This is a common syntax in Go—we call it literal construction. Here, we’re doing a literal construction of type `example`, initializing fields `counter` and `pi`. Notice the colon syntax and the comma at the end of each line. Go is big on convention over configuration. It’s opinionated. `gofmt` will enforce these rules, so you might as well accept them.

You’ll also see the short variable declaration operator (`:=`) used here. Go uses the dot operator—`value.field`—just like other languages.

You might see literal construction used even when setting a value to zero. There’s nothing wrong with that. But I don’t do it. I prefer `var` to clearly indicate zero value construction, like on line 20. Line 27 is also zero value construction in this case.

But here’s the thing: just because literal construction creates a zero value here doesn’t mean it always does. So I’d rather use `var` to make the intent clear. There’s nothing wrong with using literal construction for zero values—if that’s your style, be consistent. In the code we’ll go through, we’ll mostly use literal construction when initializing to non-zero values.

The only time I might use empty literal construction is when I’m not assigning to a variable—like in a return statement or a function call. That’s acceptable. But when assigning to a variable, I won’t use empty literal construction.

That’s the basic idea of struct layout and user-defined structs. But now let’s look at something more interesting: anonymous structs. We call these literals in the language. Any time you see a compiler message about a "literal type" or "literal value," it refers to an unnamed type. Some things in Go are named, some are unnamed.

On line 14, we’re declaring a variable `e1` based on a literal struct type. I can’t give it a name—it’s unnamed. We’re using `var`, so it’s set to zero value. Readability is about consistency, and using `var` gives a consistent look for zero value construction.

On line 21, I display `e1`, which will show all zero values. Here, I’m doing literal construction—actually, this is a type declaration on the fly. It’s a literal struct with no name. Below it, you see literal construction again, but this time initializing fields—so not zero value.

You’ll see this a lot in Go. Literal types are handy—for example, when unmarshaling data from a web API. A piece of data comes in, you need a type to decode it, but it’s not worth naming because it’s only used once. Naming it would be pollution. With literal structs, you can define them right where they’re needed and use them immediately. It enhances readability because the struct is defined in context.

Now let’s focus on the `e2` variable, because I want to talk about something else. Take this struct declaration and define two types:

```go
type bill struct {
    flag    bool
    counter int16
    pi      float32
}

type alice struct {
    flag    bool
    counter int16
    pi      float32
}
```

Notice that `bill` and `alice` are technically identical. They have the same memory layout, same field names, same types. If I have a value of type `bill` or `alice`, I should be able to assign them to each other without any integrity issues—their layouts are identical.

So let’s try it. I’ll create a variable of type `bill` and one of type `alice`, both zero value. Then I’ll assign `alice` to `bill`. The compiler will complain if I don’t use the variables, so I’ll print them. But here’s the question: even though `bill` and `alice` are identical and compatible, the compiler says: "Cannot use `alice` as type `bill` in assignment."

It refuses the assignment. Many other languages would allow this—they’d perform an implicit conversion. But Go doesn’t. And this goes back to Martin Thompson’s quote: implicit conversion is dangerous.

Implicit conversion has caused a tremendous amount of pain in software over the decades. It’s not necessarily a problem with structs, but traditionally it’s been a problem with integers—say, assigning a signed `int` to an unsigned `int`. That can cause loss of precision and data corruption.

Even when data is identical and compatible, implicit conversion has caused more problems than good. Go says: when working with concrete named types like `bill` and `alice`, I won’t do implicit conversion because historically it causes more harm than benefit.

But what if we really need to do the assignment? That’s where explicit conversion comes in. I love Go’s conversion syntax because it shows intent. It tells the compiler: "Yes, I intend for this assignment to happen." There’s no ambiguity. The compiler can’t assume—it requires you to be explicit.

So we write:

```go
b = bill(a)
```

Now the compiler knows it’s intentional. This is a beautiful part of the language—it holds us to integrity and responsibility in our code.

Now here’s an interesting question. We have `e2`, a variable based on an anonymous struct that’s identical and compatible with `bill` and `alice`. What if I try to assign `e2` to `bill`? Will the compiler require conversion?

No—it won’t. The compiler allows the assignment. The difference is that `alice` is a named type, but `e2` is based on a literal (unnamed) type. When types are named, Go requires explicit conversion—no implicit conversion. But when a type is unnamed—a literal type—Go allows more flexibility in assignment.

It’s not really implicit conversion; it’s more that Go recognizes compatibility between unnamed types and allows safe assignment. This is especially useful with function types. A function in Go is a literal value—it’s not a named type. We pass functions to functions all the time, especially in web APIs and middleware. This flexibility makes that convenient without sacrificing safety.

So I love the balance Go provides: for named types, no implicit conversion—you must show intent with explicit conversion. For literal (unnamed) types, you get flexibility because compatibility is clear and safe. Integrity is maintained.

Remember: Go isn’t being novel. The keyword `struct` is our primary way of defining user-defined data. We don’t have `class` like in other languages. We have `struct`. `struct` defines data.

There’s no implicit conversion for concrete named types. We have literal types. Conversion syntax is used for explicit conversions.

We also talked about not optimizing for performance prematurely. Always optimize for correctness first. In the first example, I want fields laid out for readability—grouped logically. But I want you to understand the cost. Knowing how much memory a type allocates helps you understand cost, which is why we discussed alignments and padding.

Understand that unless a benchmark shows you’re using too much memory, you shouldn’t restructure your structs just to minimize padding. I cannot stress this enough. I’ve had clients come in after this lesson and restructure everything to reduce padding. Please don’t do that. I’m teaching you this so we can understand allocation cost—not so you can restructure every struct.