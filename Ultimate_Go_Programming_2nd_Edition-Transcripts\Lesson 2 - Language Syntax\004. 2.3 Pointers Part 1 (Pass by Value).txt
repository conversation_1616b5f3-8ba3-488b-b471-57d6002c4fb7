Pointers. This is one of the most important sections we're going to have in the class, and it's really going to help us learn how we can look at the impact that our code is having on the machine. Remember, I said that performance—or the lack of performance—comes from four places. One is latency around networking and disk I/O; that type of delay. We're not going to talk about that in this class. The second is around allocations and memory, garbage collection—this is where we're going to learn how all of that works. The third is how we access data, and eventually, algorithm efficiency. This is the beginning of being able to understand the impact our code has on the machine as it relates to memory.

Let's start with the idea that everything in Go is pass by value. When I say pass by value, I mean WYSIWYG—what you see is what you get. I want to give you an initial example of what we mean by pass by value and how this WYSIWYG principle allows us to truly understand the impact our program is having.

Before I dive into the code, there are a couple of things I want to share upfront. We're going to talk in much deeper detail about these concepts when we get to the concurrency sections, but understanding them now will help us grasp the impacts on this code and what we'll be doing moving forward.

When your Go program starts up, it's given a P—a logical processor—for every core identified on the host machine. From a playground or single-threaded perspective, that means one core or one P. That <PERSON> is given a real, live operating system thread, which we call an M. I promise I'll go much deeper into this during the concurrency sections—don't skip ahead. Stay with me here.

That M is still scheduled by the operating system scheduler on a particular core. And we get one more thing from the runtime: our goroutine. There it is—our G. If you've ever heard of coroutines before, goroutines are coroutines; Go just changed the C to a G.

What's important right now is the idea of a path of execution. Threads are our path of execution at the operating system level. All the code you write eventually becomes machine code, and the operating system's job is to choose a path of execution—a thread—to execute those instructions one after another, starting from main: bop, bop, bop, bop, bop. That's the job of the thread. The operating system schedules threads.

But what's important here now are the data structures. There are three areas of memory we may talk about throughout the class: the data segment, stacks, and heaps. The data segment is usually reserved for global variables and read-only values—we don't care about that so much today. What we're going to focus on is stacks and heaps.

A stack is a data structure that every thread is given. At the operating system level, your stack is a contiguous block of memory, usually allocated to be one megabyte. One megabyte of memory for every stack, and therefore for every thread. If you had 10,000 threads, you can do the math—that's 10,000 megabytes of memory immediately used out of the box.

This concept of a stack comes all the way from the hardware. The hardware wants this. It really helps simplify our programming models down there, and it's going to help us too in our programming models, as we'll see, to understand cost and what's happening.

Since an M—an operating system thread—is a path of execution and has a stack, and it needs that stack to do its job, our G—our path of execution at Go's level—is very much like an M. We could almost say they're the same, but this is above the operating system. A G also has a stack of memory. Except in Go today, that stack is 2KB in size—2KB. It was 4KB for a long time; today it's 2KB. Notice how much smaller—significantly smaller—the stack is for a goroutine compared to an operating system thread. That's important because we want to be able to have a lot of goroutines in our program—lots of paths of execution running.

Remember, Go is focused on both integrity and minimizing the use of resources throughout the running program. This is one of those areas where we're seeing that.

When our Go program starts up, we get a G. Our G is our path of execution. Its job is to execute every single instruction we've written. The M hosts the G on top of it to actually make that happen at the hardware level.

What we have is this 2KB stack, and this is what's important here—this is what we're going to be talking about. By the time the goroutine created for this Go program wants to execute main, it has already executed a bunch of code from the runtime. Eventually it says, "Hey, I want to execute the main function now—we're ready."

Here's what happens: as the goroutine executes code and starts jumping between functions, this stack becomes critical to making all of that happen. Eventually, the goroutine says it wants to execute main. And any time a goroutine makes a function call, it takes some memory off the stack. We call this a frame of memory. It slices a frame of memory off the stack so it can now execute the code inside main.

What I want you to understand right now is that in order to execute this data transformation, we have to be able to read and write memory. Remember, every line of code we write is either reading memory, writing memory, or allocating memory. Those are the three things it's doing.

And what's important here is that the goroutine only has direct access to memory for the frame it is currently operating on. Let's think about this. The goroutine only has direct access to memory within the frame it's executing in. It wants to execute main—this is now our active frame. The goroutine is now executing within this context, and for our purposes right now, this is the only memory the goroutine can read and write to directly. This is it.

What does that mean to us? It means that if this data transformation has to be executed by the goroutine, and it can only operate within the scope of memory in this frame, then all of the data the goroutine needs to perform this transformation has to be in this frame.

If we look on line 10, we see a variable declaration: count assigned to the value 10. We're now allocating four bytes of memory right here inside this frame. It has to be inside this frame because if it's not, the goroutine can't access it.

Understand that this frame serves a really important purpose: it creates a sandbox, a layer of isolation. It gives us a sense of immutability—the goroutine can only mutate or cause problems here and nowhere else in our code. This is a very powerful construct that we're going to want to leverage, and it starts to allow us to talk about things like semantics.

You're going to hear me use the words "mechanics" and "semantics." When I talk about mechanics, I'm talking about how things work. When I talk about semantics, I'm talking about how things behave. The semantics let us understand the behavior and the impact we're going to have. The mechanics allow us to visualize and see how all of that works. We need both. But the semantics are very important and powerful.

So far, we've created a value of type int, its value is 10, and it has been allocated or created within the frame because this is the only place the goroutine can operate in terms of memory.

On line 13, we're doing two things. I want to bring everything back to English. On line 13, I'm asking us to display the value of count. Any time I use the variable's name, I want us to think "value of"—what's in the box?

Over here, you see I'm using the ampersand operator. The ampersand operator isn't unique to Go—many programming languages have used it, and it usually means the same thing: "address of"—where is the box? If you have a box in memory, it has to be somewhere. We're going to use hexadecimal numbers for this. We only need to worry about the last four digits. I could pretend this is an address: F1BC. Again, we use hexadecimal because it's more efficient when dealing with very large numbers like memory addresses.

"Value of"—what's in the box? That's the variable and only the variable. "Ampersand variable"—address of—where is the box? It has to be located somewhere in memory. It's a memory address. There it is.

Now look at line 16. On line 16, we're about to make another function call. Think about this: every time you make a function call, you're crossing a program boundary. Program boundaries are important to identify because, in this case, this function call means we're moving out of this sandbox or frame and into a new one.

Every time we call a function, we slice a new frame off the stack. This becomes the active frame. Our goroutine now operates within this sandbox—this level of isolation—which means we'll execute the code inside increment, perform this new data transformation, and the data the goroutine needs to perform this transformation must be inside this frame because this is the only place we can operate.

This is where the idea of parameters comes from. We've used parameters our whole programming lives. We've learned about API design and done all these things, but I want to show you the mechanics behind them because without the mechanics of parameters, we couldn't encapsulate at all.

These parameters serve a mechanical purpose on top of their design purpose: to get data inside this new frame so the goroutine can perform this data transformation in isolation, with a level of immutability.

On line 16, we're passing the value of count across this program boundary. Because everything in Go is pass by value, that means we're making a copy of the data as it goes across the boundary.

You're going to hear me use three different terms. You'll hear me say "data"—that's what we're working with. Concrete data. If you don't understand the data, you don't understand the problem.

There are two types of data we operate with: the value itself—like this integer value 10—or the value's address. Yes, addresses are data, and I want you to always understand that.

In this case, what we're passing across the program boundary is the value itself, which means we're making a copy of the value—the four bytes. We're throwing it over this program boundary, which means those four bytes now have to end up inside this frame as well. This is where your parameters come from.

The parameter we're declaring inside inc—increment, sorry—int, is there to capture or hold that value we're throwing over the program boundary so the goroutine can operate on this data transformation. This becomes inc.

Passed by value means we make copies and we store copies. There it is.

Now the goroutine can operate within this function, which it does. On line 27, you see a read-modify-write operation: inc++. That means we are now mutating memory—but we're mutating it right here.

What's really important is that this frame allows the goroutine to mutate memory without causing side effects throughout the program. The memory mutation is happening in isolation within our sandbox, with a level of mutability in the sense that we don't affect anything else outside of this execution context.

This is a very important and powerful mechanism.

What we're really seeing here is what's called value semantics. We're going to focus a lot in this class on the value and pointer semantics behavior that the language gives you. If you want to write code in Go that is optimized for correctness—that you can read and understand the impact of—then your value and pointer semantics are everything.

What we're looking at here is an example of value semantics. There's a value in main for count. Now we perform a different transformation on that data, which means that every data transformation—every piece of code operating on it—gets its own copy of the value. This is value semantics. It's very important.

Value semantics has the benefit of isolation and immutability. It can also, in many cases, give us some performance. We'll talk about that. But I told you: engineering isn't about just hacking code—it's about knowing the cost of things. Everything has a cost; nothing is free.

So the question now is: what is the cost of value semantics?

One of the costs of value semantics is that we have multiple copies of the data throughout the program. There is no efficiency with value semantics, and sometimes it can be very complicated to get a piece of data that's changed and propagate that update everywhere it needs to be.

Our value semantics are very powerful because they reduce things like side effects—we'll talk about that. They give us isolation, they give us levels of immutability that are so important for integrity. But sometimes, the inefficiency of value semantics might cause more complexity in the code. It might even cause performance problems—and performance does matter.

One of the things we have to learn is how to balance our value and pointer semantics.

Right now, all we're looking at is value semantics.

When the increment function returns and we're back in main, this goroutine is no longer operating in that active frame. This is now our active frame. The goroutine is operating here. If I were to run this program, we'd see that the mutation we made was isolated here—it did not carry up or forward. This piece of code is still operating on its own copy. That was operating on its own copy. We got the benefit of this mutation not affecting anything else.

This is value semantics, and this is a big part—again—of the beauty of pass by value. What we see is what you get.