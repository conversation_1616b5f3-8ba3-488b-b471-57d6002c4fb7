Alright, so we've just learned the method mechanics and semantics; we've just finished interface mechanics and semantics. There are a couple more mechanics and semantics we need to learn around decoupling before we can get into design, and this one is embedding. Embedding is a very important and powerful feature in Go. We're going to use it around composition, which is a design pattern that we use heavily in Go.

Let's take a look at a base piece of code that we're going to start with here. In this code, we have our user-defined type—or concrete type—user, with fields name and email, and then we have our method named notify using pointer semantics. Again, we're going to be specifying what semantics are in play. Without semantics in play, we don't understand behavior and impact. So we've got a notify method using pointer semantics. Nothing special is going on here.

I've got the admin type. It's another concrete type named admin, and admin has two fields: person and level. The person field happens to be based on the other concrete type, user—a struct type. Okay, great, but nothing special. This is not embedding just yet; it's just two different, unique fields, just like level is a string.

Now, on line 33, we construct the admin value. Because the person field is also of a struct type, we do our struct literal construction inside and initialize the person field to be of this user. Okay, great. Then nothing special—I can access the notify behavior through the person field via our admin variable. This is just setting up; nothing special going on here. We've done this throughout our programming lives.

But let's make a change to this piece of software. We go back to our admin type. User has name and email again. Our pointer semantics on notify. On admin, we've removed the person field and instead embedded a user value directly inside the admin value. This is what I want you to think about for a second: we are embedding a user value inside the admin value. But a better way of thinking about this is to create an inner type and outer type relationship. I want you to look at user as the inner type and admin as the outer type. When you do that, we get this concept of inner type promotion. Everything related to the inner type can be promoted up to the outer type. In other words, promotion allows access to those things from the inner type through the outer type.

Let's do our construction like we did before. Construction is very tedious in Go—types have to be very explicit—but you can see here that the user type looks and feels like a field during construction. It's really not a field. We're just trying to access the user value that we've embedded directly, but we do it through the type's name. So now we're initializing the inner value directly.

Here's where inner type promotion really comes in: we can still access the notify behavior through the inner type value directly, but because of inner type promotion, I can also access notify directly through the outer type value. Look—both calls produce the exact same output. Inner type promotion is giving us a sense of type reuse, but we're going to use it for much more than that.

Now, let's add a little more complexity to this program. Let's go ahead and add an interface—the notifier interface with the active behavior notify. Again, our inner type user implements the notifier interface using pointer semantics. Brilliant.

We still embed user inside admin. We do our construction. But here's the cool part: we add a polymorphic function called sendNotification. What does sendNotification say? Pass me any piece of concrete data—any value or any pointer—that implements the notifier interface. Remember, it can't ask for notifier values; they don't exist. Interfaces are valueless. That being said, pass me any concrete piece of data—any value, any pointer—that implements the notifier interface.

And because of inner type promotion, the outer value—ad, the address to it, because we use pointer semantics on the implementation—satisfies the interface. Think about how amazing this is.

Embedding does not create a subtyping relationship. Embedding does not create a subtyping relationship. You cannot pass an admin value around as a user. This isn't base-derived class stuff like you see in object-oriented programming languages. Admin is admin, and user is user. But because of inner type promotion—because the behavior is being promoted up—this admin value now satisfies all the same interfaces, all the same contracts, as the inner type did, because we can promote that method set up. This is really, really powerful.

Remember that our work is done in the concrete—concrete data is where our performance comes from, that's where our problems are solved. And decoupling helps us deal with change. Layers of decoupling—this is what we're looking at. Architecture and design—all of this is happening through behavior. Now this outer type, this concrete piece of data, admin, has the right behaviors because of inner type promotion to satisfy the notifier interface.

Now let's keep going and add one more interesting piece to this code. We've got the notifier interface with the active behavior notify. I've got my user, the concrete type, my inner type implementing the notifier interface—brilliant. But this time, admin—the outer type—also implements the notifier interface. Look at that. I now have two implementations of the notifier interface, two implementations of the method notify: one from the inner type, one from the outer type.

But when the outer type implements the same method as the inner type, there is no promotion. The outer type overrides the inner type's implementation. Now, when we call our polymorphic function with the address to the outer type, it is the outer type's implementation that gets called. The same thing happens when we call notify against the outer type's value. But in this particular case, we can always still access the inner type's behavior through the inner type value directly, like we just did.

It's all there. So when I said that inner type promotion can happen, I meant *can*—because only at compile time does the compiler really check, and only at compile time do those things that are being accessed get promoted up.

One of the very first questions a lot of people ask in class is: "What if I have two inner types that implement the same method—what's going to happen?" Well, nothing's going to happen unless you try to make a method call that creates ambiguity. You can have all of that as long as you don't make the call. So, until you access something, it is not promoted, and at that point the compiler will validate that there's no ambiguity. And life is good.

So, embedding looks like it's part of this concept of type reuse in Go. But we're never really going to use it for type reuse. We want to use it for composition—which we're going to be seeing very, very soon.