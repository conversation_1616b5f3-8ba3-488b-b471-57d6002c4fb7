There's another version of the fan-out pattern that we call the fan-out semaphore. This pattern allows us to fan out into a number of goroutines, but instead of letting all of them run simultaneously, we limit how many can actually execute at the same time. We create a dynamic pool of goroutines and control concurrency by restricting how many are actively running, while the rest remain in a runnable state. This helps reduce the latency associated with goroutine creation while also limiting the impact these goroutines have on a shared resource. This is the fan-out semaphore pattern.

Let’s walk through it. I have a buffered channel of size 20 because we’re going to launch 20 goroutines. These goroutines will signal using string data, and we want to minimize the latency between sends and receives. Now, here’s the semaphore channel, which I’ve set to a buffer size of five. This means that at any given time, only five out of the 20 goroutines can be actively executing. We still pay the cost of creating all 20 goroutines upfront, which reduces latency in spawning them on demand, but we cap the number that can run concurrently. The rest will be ready and waiting in the runnable state until a slot opens up.

On line 160, I launch all 20 goroutines—same as before. But now, look at line 162: each goroutine performs a send on the semaphore channel, which is buffered with a capacity of five. This send operation will block if the buffer is full. So even though all 20 goroutines are launched immediately, they all hit this send and block, waiting for space in the semaphore channel. They’re blocked because the send can only proceed if there’s room in the buffer.

Right now, we’ve allocated five slots, but for illustration, let’s pretend the semaphore count is only two—just to fit on the whiteboard. We launch all the goroutines; they’re all in the runnable state. The scheduler picks one to run first—we can’t predict which one. Say it’s this one. It attempts the channel send on line 162. Since the buffer has space (we’re at zero, capacity two), the send succeeds immediately. The semaphore count goes from zero to one, and that goroutine proceeds to do its work.

Then another goroutine is scheduled—maybe this one. It also performs the send, sees there’s still space (one slot used, one free), sends its data (just a boolean flag in this case), and the count goes to two. Now the buffer is full.

The next goroutine to run—say this one—tries to send, but the buffer is full, so it blocks. Every other goroutine trying to send will also block. They all wait because there’s no room in the semaphore channel.

Now we’re waiting for one of the active goroutines to finish its work. After an unknown amount of time, one of them completes its task and sends its result back to the caller, just like before. But now, critically, it performs a receive on the semaphore channel. This receive pulls data out of the channel, freeing up a buffer slot. The count drops from two back to one.

Suddenly, one of the blocked goroutines can unblock—the scheduler selects one at random from our perspective—and it completes its send. The count goes back to two. Then another goroutine finishes, performs a receive, frees another slot, and another waiting goroutine unblocks.

This continues: before a goroutine can do its actual work, it must send a value into the semaphore channel. If it succeeds, it proceeds. When it finishes, it removes that value via a receive, making room for another goroutine to start. The cost of creating goroutines is paid upfront, so they’re all ready, all blocked on that initial send. As space becomes available in the buffer, the scheduler picks goroutines to proceed, they do their work, release the slot, and the cycle continues until all 20 complete.

We use this pattern when we’re dealing with a limited resource. If 20 goroutines hit that resource simultaneously, we might overwhelm it. But with only five active at a time, we stay within safe limits. However, this pattern introduces additional latency—every goroutine must wait for a slot, even if the system could handle more. So you have to weigh the engineering trade-offs: the added latency versus the protection it gives to the system under load, and understand the backpressure you’re introducing at scale.